## Proposed Plan: Enhancing Flutter HVAC CRM with Material Design 3 and Kratos API Integration

The goal is to create new UI elements for the CRM system, focusing on Material Design 3 principles, and integrate data from the GoBackend-Kratos API.

### Phase 1: UI Component Development (Material Design 3)

This phase focuses on building reusable Material Design 3 components that align with the project's design philosophy.

1.  **Review and Extend Existing Material Design 3 Components:**
    *   Examine `hvac_flutter_md3/lib/core/theme/app_theme.dart` to ensure new components adhere to the defined theme.
    *   Identify existing Material Design 3 components (e.g., `Card`, `ElevatedButton`, `Chip`) used in `01_Material_Design_3_Expressive_Integration.md` and `02_Unified_Architecture_Integration_Guide.md`.
    *   Create a new directory, e.g., `hvac_flutter_md3/lib/shared/widgets/md3_components/`, for custom Material Design 3 widgets that can be reused across the application.

2.  **Develop Core UI Elements for CRM:**
    *   **Enhanced Data Display Cards:** Create a generic `CRMDataCard` widget that can display various types of CRM data (e.g., customer details, equipment status, job summaries) with Material Design 3 styling, dynamic colors, and interactive elements (e.g., action buttons, status chips). This will be used in Customer Management, Equipment Management, and Technician Dashboard.
    *   **Filter and Sort Controls:** Implement Material Design 3 `FilterChip` and `ChoiceChip` widgets for filtering and sorting data in lists (e.g., customer lists, job lists, equipment lists).
    *   **Data Entry Forms:** Design and implement Material Design 3 forms for creating/editing CRM entities (e.g., new customer, new job, equipment details). This will involve `TextField`, `DropdownMenu`, `DatePicker`, and `TimePicker` widgets.

### Phase 2: Kratos API Integration and Data Models

This phase focuses on integrating with the GoBackend-Kratos API to fetch and display CRM data.

1.  **Define/Refine Data Models:**
    *   Review existing models in `hvac_flutter_md3/lib/shared/models/` (e.g., `customer_model.dart`, `equipment_status_model.dart`, `job_model.dart`).
    *   Based on the required CRM features (Customer Management, Equipment Management, Analytics), identify any missing fields or new models needed to fully represent the data from the GoBackend-Kratos API.
    *   Ensure models include `fromJson` and `toJson` methods for serialization/deserialization with the REST API.

2.  **Implement/Extend API Services:**
    *   **Customer Service:** Extend `hvac_flutter_md3/lib/shared/services/customer_service.dart` to include methods for fetching detailed customer profiles, updating customer information, and potentially adding new customers.
    *   **Equipment Service:** Extend `hvac_flutter_md3/lib/shared/services/equipment_service.dart` to include methods for fetching equipment details, maintenance history, and updating equipment status.
    *   **New Services (if needed):** If there are new CRM entities (e.g., service contracts, invoices) that are not covered by existing services, create new service files (e.g., `hvac_flutter_md3/lib/shared/services/contract_service.dart`).
    *   Ensure all API calls handle authentication using `KratosAuthService` (session tokens).

3.  **Integrate with State Management (Riverpod):**
    *   Review existing providers in `hvac_flutter_md3/lib/shared/providers/` (e.g., `customer_provider.dart`, `equipment_provider.dart`).
    *   Create new Riverpod providers or extend existing ones to manage the state of the new CRM data fetched from the API. This will involve `AsyncNotifierProvider` or `StreamProvider` for real-time updates if gRPC is used for specific data streams.
    *   Implement error handling and loading states within the providers.

### Phase 3: Feature Implementation (CRM Modules)

This phase combines the UI components and API integration to build specific CRM features.

1.  **Customer Profile View:**
    *   Create a new screen or extend `hvac_flutter_md3/lib/features/customer_management/presentation/customer_profile_widget.dart` to display a comprehensive customer profile using the new `CRMDataCard` and other Material Design 3 components.
    *   Integrate with `CustomerService` and `CustomerProvider` to fetch and display customer data.
    *   Add functionality to edit customer details and save changes back to the API.

2.  **Equipment Details View:**
    *   Create a new screen or extend an existing widget to display detailed equipment information, including maintenance history and performance metrics.
    *   Utilize the new `CRMDataCard` and other Material Design 3 components for a rich display.
    *   Integrate with `EquipmentService` and `EquipmentProvider` to fetch and display equipment data.

3.  **Analytics Dashboard Enhancements:**
    *   Enhance `hvac_flutter_md3/lib/features/analytics/presentation/analytics_dashboard_widget.dart` with new charts and data visualizations using `fl_chart` and Material Design 3 colors, as outlined in `01_Material_Design_3_Expressive_Integration.md`.
    *   Integrate with relevant API services and providers to fetch data for analytics.

### Phase 4: Testing and Refinement

1.  **Unit and Widget Testing:** Write tests for new UI components, API services, and Riverpod providers.
2.  **Integration Testing:** Test the end-to-end flow of fetching data from Kratos API and displaying it in the UI.
3.  **Material Design 3 Compliance:** Ensure all new UI elements strictly adhere to Material Design 3 guidelines for consistency and user experience.
4.  **Performance Optimization:** Monitor and optimize the performance of new UI elements and API calls.

### Mermaid Diagram:

```mermaid
graph TD
    A[Start Task: Enhance Flutter HVAC CRM] --> B{Phase 1: UI Component Development};

    B --> B1[Review & Extend MD3 Components];
    B1 --> B2[Develop Core UI Elements];
    B2 --> C{Phase 2: Kratos API Integration};

    C --> C1[Define/Refine Data Models];
    C1 --> C2[Implement/Extend API Services];
    C2 --> C3[Integrate with State Management (Riverpod)];
    C3 --> D{Phase 3: Feature Implementation};

    D --> D1[Customer Profile View];
    D1 --> D2[Equipment Details View];
    D2 --> D3[Analytics Dashboard Enhancements];
    D3 --> E{Phase 4: Testing & Refinement};

    E --> E1[Unit & Widget Testing];
    E1 --> E2[Integration Testing];
    E2 --> E3[MD3 Compliance];
    E3 --> E4[Performance Optimization];
    E4 --> F[Task Complete];

    subgraph UI Components
        B1;
        B2;
    end

    subgraph API & Data
        C1;
        C2;
        C3;
    end

    subgraph CRM Features
        D1;
        D2;
        D3;
    end

    subgraph Quality Assurance
        E1;
        E2;
        E3;
        E4;
    end