Compiling the CXX compiler identification source file "CMakeCXXCompilerId.cpp" failed.
Compiler: /snap/flutter/current/usr/bin/clang++ 
Build flags: 
Id flags:  

The output was:
1
/snap/flutter/current/usr/bin/ld: /lib/x86_64-linux-gnu/libm.so.6: unknown type [0x13] section `.relr.dyn'
/snap/flutter/current/usr/bin/ld: skipping incompatible /lib/x86_64-linux-gnu/libm.so.6 when searching for /lib/x86_64-linux-gnu/libm.so.6
/snap/flutter/current/usr/bin/ld: cannot find /lib/x86_64-linux-gnu/libm.so.6
/snap/flutter/current/usr/bin/ld: /lib/x86_64-linux-gnu/libmvec.so.1: unknown type [0x13] section `.relr.dyn'
/snap/flutter/current/usr/bin/ld: skipping incompatible /lib/x86_64-linux-gnu/libmvec.so.1 when searching for /lib/x86_64-linux-gnu/libmvec.so.1
/snap/flutter/current/usr/bin/ld: cannot find /lib/x86_64-linux-gnu/libmvec.so.1
clang: error: linker command failed with exit code 1 (use -v to see invocation)


