# CMAKE generated file: DO NOT EDIT!
# Generated by "Ninja" Generator, CMake Version 3.16

# This file contains all the build statements describing the
# compilation DAG.

# =============================================================================
# Write statements declared in CMakeLists.txt:
# 
# Which is the root file.
# =============================================================================

# =============================================================================
# Project: runner
# Configuration: Debug
# =============================================================================

#############################################
# Minimal version of Ninja required by this file

ninja_required_version = 1.5

# =============================================================================
# Include auxiliary files.


#############################################
# Include rules file.

include rules.ninja


#############################################
# Utility command for install/strip

build CMakeFiles/install/strip.util: CUSTOM_COMMAND all
  COMMAND = cd /home/<USER>/HVAC/FlutterHVAC-MD3/hvac_flutter_md3/build/linux/x64/debug && /snap/flutter/149/usr/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
  DESC = Installing the project stripped...
  pool = console
  restat = 1

build install/strip: phony CMakeFiles/install/strip.util


#############################################
# Utility command for install/local

build CMakeFiles/install/local.util: CUSTOM_COMMAND all
  COMMAND = cd /home/<USER>/HVAC/FlutterHVAC-MD3/hvac_flutter_md3/build/linux/x64/debug && /snap/flutter/149/usr/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
  DESC = Installing only the local directory...
  pool = console
  restat = 1

build install/local: phony CMakeFiles/install/local.util


#############################################
# Utility command for install

build CMakeFiles/install.util: CUSTOM_COMMAND all
  COMMAND = cd /home/<USER>/HVAC/FlutterHVAC-MD3/hvac_flutter_md3/build/linux/x64/debug && /snap/flutter/149/usr/bin/cmake -P cmake_install.cmake
  DESC = Install the project...
  pool = console
  restat = 1

build install: phony CMakeFiles/install.util


#############################################
# Utility command for list_install_components

build list_install_components: phony


#############################################
# Utility command for rebuild_cache

build CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cd /home/<USER>/HVAC/FlutterHVAC-MD3/hvac_flutter_md3/build/linux/x64/debug && /snap/flutter/149/usr/bin/cmake -S/home/<USER>/HVAC/FlutterHVAC-MD3/hvac_flutter_md3/linux -B/home/<USER>/HVAC/FlutterHVAC-MD3/hvac_flutter_md3/build/linux/x64/debug
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build rebuild_cache: phony CMakeFiles/rebuild_cache.util


#############################################
# Utility command for edit_cache

build CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cd /home/<USER>/HVAC/FlutterHVAC-MD3/hvac_flutter_md3/build/linux/x64/debug && /snap/flutter/149/usr/bin/cmake -E echo No\ interactive\ CMake\ dialog\ available.
  DESC = No interactive CMake dialog available...
  restat = 1

build edit_cache: phony CMakeFiles/edit_cache.util

# =============================================================================
# Write statements declared in CMakeLists.txt:
# /home/<USER>/HVAC/FlutterHVAC-MD3/hvac_flutter_md3/linux/CMakeLists.txt
# =============================================================================


#############################################
# Utility command for install/strip

build flutter/CMakeFiles/install/strip.util: CUSTOM_COMMAND flutter/all
  COMMAND = cd /home/<USER>/HVAC/FlutterHVAC-MD3/hvac_flutter_md3/build/linux/x64/debug/flutter && /snap/flutter/149/usr/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
  DESC = Installing the project stripped...
  pool = console
  restat = 1

build flutter/install/strip: phony flutter/CMakeFiles/install/strip.util


#############################################
# Utility command for install/local

build flutter/CMakeFiles/install/local.util: CUSTOM_COMMAND flutter/all
  COMMAND = cd /home/<USER>/HVAC/FlutterHVAC-MD3/hvac_flutter_md3/build/linux/x64/debug/flutter && /snap/flutter/149/usr/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
  DESC = Installing only the local directory...
  pool = console
  restat = 1

build flutter/install/local: phony flutter/CMakeFiles/install/local.util


#############################################
# Utility command for install

build flutter/CMakeFiles/install.util: CUSTOM_COMMAND flutter/all
  COMMAND = cd /home/<USER>/HVAC/FlutterHVAC-MD3/hvac_flutter_md3/build/linux/x64/debug/flutter && /snap/flutter/149/usr/bin/cmake -P cmake_install.cmake
  DESC = Install the project...
  pool = console
  restat = 1

build flutter/install: phony flutter/CMakeFiles/install.util


#############################################
# Utility command for list_install_components

build flutter/list_install_components: phony


#############################################
# Utility command for rebuild_cache

build flutter/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cd /home/<USER>/HVAC/FlutterHVAC-MD3/hvac_flutter_md3/build/linux/x64/debug/flutter && /snap/flutter/149/usr/bin/cmake -S/home/<USER>/HVAC/FlutterHVAC-MD3/hvac_flutter_md3/linux -B/home/<USER>/HVAC/FlutterHVAC-MD3/hvac_flutter_md3/build/linux/x64/debug
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build flutter/rebuild_cache: phony flutter/CMakeFiles/rebuild_cache.util


#############################################
# Utility command for edit_cache

build flutter/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cd /home/<USER>/HVAC/FlutterHVAC-MD3/hvac_flutter_md3/build/linux/x64/debug/flutter && /snap/flutter/149/usr/bin/cmake -E echo No\ interactive\ CMake\ dialog\ available.
  DESC = No interactive CMake dialog available...
  restat = 1

build flutter/edit_cache: phony flutter/CMakeFiles/edit_cache.util


#############################################
# Utility command for flutter_assemble

build flutter/flutter_assemble: phony flutter/CMakeFiles/flutter_assemble /home/<USER>/HVAC/FlutterHVAC-MD3/hvac_flutter_md3/linux/flutter/ephemeral/libflutter_linux_gtk.so /home/<USER>/HVAC/FlutterHVAC-MD3/hvac_flutter_md3/linux/flutter/ephemeral/flutter_linux/fl_basic_message_channel.h /home/<USER>/HVAC/FlutterHVAC-MD3/hvac_flutter_md3/linux/flutter/ephemeral/flutter_linux/fl_binary_codec.h /home/<USER>/HVAC/FlutterHVAC-MD3/hvac_flutter_md3/linux/flutter/ephemeral/flutter_linux/fl_binary_messenger.h /home/<USER>/HVAC/FlutterHVAC-MD3/hvac_flutter_md3/linux/flutter/ephemeral/flutter_linux/fl_dart_project.h /home/<USER>/HVAC/FlutterHVAC-MD3/hvac_flutter_md3/linux/flutter/ephemeral/flutter_linux/fl_engine.h /home/<USER>/HVAC/FlutterHVAC-MD3/hvac_flutter_md3/linux/flutter/ephemeral/flutter_linux/fl_json_message_codec.h /home/<USER>/HVAC/FlutterHVAC-MD3/hvac_flutter_md3/linux/flutter/ephemeral/flutter_linux/fl_json_method_codec.h /home/<USER>/HVAC/FlutterHVAC-MD3/hvac_flutter_md3/linux/flutter/ephemeral/flutter_linux/fl_message_codec.h /home/<USER>/HVAC/FlutterHVAC-MD3/hvac_flutter_md3/linux/flutter/ephemeral/flutter_linux/fl_method_call.h /home/<USER>/HVAC/FlutterHVAC-MD3/hvac_flutter_md3/linux/flutter/ephemeral/flutter_linux/fl_method_channel.h /home/<USER>/HVAC/FlutterHVAC-MD3/hvac_flutter_md3/linux/flutter/ephemeral/flutter_linux/fl_method_codec.h /home/<USER>/HVAC/FlutterHVAC-MD3/hvac_flutter_md3/linux/flutter/ephemeral/flutter_linux/fl_method_response.h /home/<USER>/HVAC/FlutterHVAC-MD3/hvac_flutter_md3/linux/flutter/ephemeral/flutter_linux/fl_plugin_registrar.h /home/<USER>/HVAC/FlutterHVAC-MD3/hvac_flutter_md3/linux/flutter/ephemeral/flutter_linux/fl_plugin_registry.h /home/<USER>/HVAC/FlutterHVAC-MD3/hvac_flutter_md3/linux/flutter/ephemeral/flutter_linux/fl_standard_message_codec.h /home/<USER>/HVAC/FlutterHVAC-MD3/hvac_flutter_md3/linux/flutter/ephemeral/flutter_linux/fl_standard_method_codec.h /home/<USER>/HVAC/FlutterHVAC-MD3/hvac_flutter_md3/linux/flutter/ephemeral/flutter_linux/fl_string_codec.h /home/<USER>/HVAC/FlutterHVAC-MD3/hvac_flutter_md3/linux/flutter/ephemeral/flutter_linux/fl_value.h /home/<USER>/HVAC/FlutterHVAC-MD3/hvac_flutter_md3/linux/flutter/ephemeral/flutter_linux/fl_view.h /home/<USER>/HVAC/FlutterHVAC-MD3/hvac_flutter_md3/linux/flutter/ephemeral/flutter_linux/flutter_linux.h flutter/_phony_


#############################################
# Phony custom command for flutter/CMakeFiles/flutter_assemble

build flutter/CMakeFiles/flutter_assemble: phony /home/<USER>/HVAC/FlutterHVAC-MD3/hvac_flutter_md3/linux/flutter/ephemeral/libflutter_linux_gtk.so /home/<USER>/HVAC/FlutterHVAC-MD3/hvac_flutter_md3/linux/flutter/ephemeral/flutter_linux/fl_basic_message_channel.h /home/<USER>/HVAC/FlutterHVAC-MD3/hvac_flutter_md3/linux/flutter/ephemeral/flutter_linux/fl_binary_codec.h /home/<USER>/HVAC/FlutterHVAC-MD3/hvac_flutter_md3/linux/flutter/ephemeral/flutter_linux/fl_binary_messenger.h /home/<USER>/HVAC/FlutterHVAC-MD3/hvac_flutter_md3/linux/flutter/ephemeral/flutter_linux/fl_dart_project.h /home/<USER>/HVAC/FlutterHVAC-MD3/hvac_flutter_md3/linux/flutter/ephemeral/flutter_linux/fl_engine.h /home/<USER>/HVAC/FlutterHVAC-MD3/hvac_flutter_md3/linux/flutter/ephemeral/flutter_linux/fl_json_message_codec.h /home/<USER>/HVAC/FlutterHVAC-MD3/hvac_flutter_md3/linux/flutter/ephemeral/flutter_linux/fl_json_method_codec.h /home/<USER>/HVAC/FlutterHVAC-MD3/hvac_flutter_md3/linux/flutter/ephemeral/flutter_linux/fl_message_codec.h /home/<USER>/HVAC/FlutterHVAC-MD3/hvac_flutter_md3/linux/flutter/ephemeral/flutter_linux/fl_method_call.h /home/<USER>/HVAC/FlutterHVAC-MD3/hvac_flutter_md3/linux/flutter/ephemeral/flutter_linux/fl_method_channel.h /home/<USER>/HVAC/FlutterHVAC-MD3/hvac_flutter_md3/linux/flutter/ephemeral/flutter_linux/fl_method_codec.h /home/<USER>/HVAC/FlutterHVAC-MD3/hvac_flutter_md3/linux/flutter/ephemeral/flutter_linux/fl_method_response.h /home/<USER>/HVAC/FlutterHVAC-MD3/hvac_flutter_md3/linux/flutter/ephemeral/flutter_linux/fl_plugin_registrar.h /home/<USER>/HVAC/FlutterHVAC-MD3/hvac_flutter_md3/linux/flutter/ephemeral/flutter_linux/fl_plugin_registry.h /home/<USER>/HVAC/FlutterHVAC-MD3/hvac_flutter_md3/linux/flutter/ephemeral/flutter_linux/fl_standard_message_codec.h /home/<USER>/HVAC/FlutterHVAC-MD3/hvac_flutter_md3/linux/flutter/ephemeral/flutter_linux/fl_standard_method_codec.h /home/<USER>/HVAC/FlutterHVAC-MD3/hvac_flutter_md3/linux/flutter/ephemeral/flutter_linux/fl_string_codec.h /home/<USER>/HVAC/FlutterHVAC-MD3/hvac_flutter_md3/linux/flutter/ephemeral/flutter_linux/fl_value.h /home/<USER>/HVAC/FlutterHVAC-MD3/hvac_flutter_md3/linux/flutter/ephemeral/flutter_linux/fl_view.h /home/<USER>/HVAC/FlutterHVAC-MD3/hvac_flutter_md3/linux/flutter/ephemeral/flutter_linux/flutter_linux.h


#############################################
# Custom command for /home/<USER>/HVAC/FlutterHVAC-MD3/hvac_flutter_md3/linux/flutter/ephemeral/libflutter_linux_gtk.so

build /home/<USER>/HVAC/FlutterHVAC-MD3/hvac_flutter_md3/linux/flutter/ephemeral/libflutter_linux_gtk.so /home/<USER>/HVAC/FlutterHVAC-MD3/hvac_flutter_md3/linux/flutter/ephemeral/flutter_linux/fl_basic_message_channel.h /home/<USER>/HVAC/FlutterHVAC-MD3/hvac_flutter_md3/linux/flutter/ephemeral/flutter_linux/fl_binary_codec.h /home/<USER>/HVAC/FlutterHVAC-MD3/hvac_flutter_md3/linux/flutter/ephemeral/flutter_linux/fl_binary_messenger.h /home/<USER>/HVAC/FlutterHVAC-MD3/hvac_flutter_md3/linux/flutter/ephemeral/flutter_linux/fl_dart_project.h /home/<USER>/HVAC/FlutterHVAC-MD3/hvac_flutter_md3/linux/flutter/ephemeral/flutter_linux/fl_engine.h /home/<USER>/HVAC/FlutterHVAC-MD3/hvac_flutter_md3/linux/flutter/ephemeral/flutter_linux/fl_json_message_codec.h /home/<USER>/HVAC/FlutterHVAC-MD3/hvac_flutter_md3/linux/flutter/ephemeral/flutter_linux/fl_json_method_codec.h /home/<USER>/HVAC/FlutterHVAC-MD3/hvac_flutter_md3/linux/flutter/ephemeral/flutter_linux/fl_message_codec.h /home/<USER>/HVAC/FlutterHVAC-MD3/hvac_flutter_md3/linux/flutter/ephemeral/flutter_linux/fl_method_call.h /home/<USER>/HVAC/FlutterHVAC-MD3/hvac_flutter_md3/linux/flutter/ephemeral/flutter_linux/fl_method_channel.h /home/<USER>/HVAC/FlutterHVAC-MD3/hvac_flutter_md3/linux/flutter/ephemeral/flutter_linux/fl_method_codec.h /home/<USER>/HVAC/FlutterHVAC-MD3/hvac_flutter_md3/linux/flutter/ephemeral/flutter_linux/fl_method_response.h /home/<USER>/HVAC/FlutterHVAC-MD3/hvac_flutter_md3/linux/flutter/ephemeral/flutter_linux/fl_plugin_registrar.h /home/<USER>/HVAC/FlutterHVAC-MD3/hvac_flutter_md3/linux/flutter/ephemeral/flutter_linux/fl_plugin_registry.h /home/<USER>/HVAC/FlutterHVAC-MD3/hvac_flutter_md3/linux/flutter/ephemeral/flutter_linux/fl_standard_message_codec.h /home/<USER>/HVAC/FlutterHVAC-MD3/hvac_flutter_md3/linux/flutter/ephemeral/flutter_linux/fl_standard_method_codec.h /home/<USER>/HVAC/FlutterHVAC-MD3/hvac_flutter_md3/linux/flutter/ephemeral/flutter_linux/fl_string_codec.h /home/<USER>/HVAC/FlutterHVAC-MD3/hvac_flutter_md3/linux/flutter/ephemeral/flutter_linux/fl_value.h /home/<USER>/HVAC/FlutterHVAC-MD3/hvac_flutter_md3/linux/flutter/ephemeral/flutter_linux/fl_view.h /home/<USER>/HVAC/FlutterHVAC-MD3/hvac_flutter_md3/linux/flutter/ephemeral/flutter_linux/flutter_linux.h flutter/_phony_: CUSTOM_COMMAND
  COMMAND = cd /home/<USER>/HVAC/FlutterHVAC-MD3/hvac_flutter_md3/build/linux/x64/debug/flutter && /snap/flutter/149/usr/bin/cmake -E env FLUTTER_ROOT=/home/<USER>/snap/flutter/common/flutter PROJECT_DIR=/home/<USER>/HVAC/FlutterHVAC-MD3/hvac_flutter_md3 DART_DEFINES=RkxVVFRFUl9WRVJTSU9OPTMuMzIuMA==,RkxVVFRFUl9DSEFOTkVMPXN0YWJsZQ==,RkxVVFRFUl9HSVRfVVJMPWh0dHBzOi8vZ2l0aHViLmNvbS9mbHV0dGVyL2ZsdXR0ZXIuZ2l0,RkxVVFRFUl9GUkFNRVdPUktfUkVWSVNJT049YmU2OThjNDhhNg==,RkxVVFRFUl9FTkdJTkVfUkVWSVNJT049MTg4MTgwMDk0OQ==,RkxVVFRFUl9EQVJUX1ZFUlNJT049My44LjA= DART_OBFUSCATION=false TRACK_WIDGET_CREATION=true TREE_SHAKE_ICONS=false PACKAGE_CONFIG=/home/<USER>/HVAC/FlutterHVAC-MD3/hvac_flutter_md3/.dart_tool/package_config.json FLUTTER_TARGET=/home/<USER>/HVAC/FlutterHVAC-MD3/hvac_flutter_md3/lib/main.dart /home/<USER>/snap/flutter/common/flutter/packages/flutter_tools/bin/tool_backend.sh linux-x64 Debug
  DESC = Generating /home/<USER>/HVAC/FlutterHVAC-MD3/hvac_flutter_md3/linux/flutter/ephemeral/libflutter_linux_gtk.so, /home/<USER>/HVAC/FlutterHVAC-MD3/hvac_flutter_md3/linux/flutter/ephemeral/flutter_linux/fl_basic_message_channel.h, /home/<USER>/HVAC/FlutterHVAC-MD3/hvac_flutter_md3/linux/flutter/ephemeral/flutter_linux/fl_binary_codec.h, /home/<USER>/HVAC/FlutterHVAC-MD3/hvac_flutter_md3/linux/flutter/ephemeral/flutter_linux/fl_binary_messenger.h, /home/<USER>/HVAC/FlutterHVAC-MD3/hvac_flutter_md3/linux/flutter/ephemeral/flutter_linux/fl_dart_project.h, /home/<USER>/HVAC/FlutterHVAC-MD3/hvac_flutter_md3/linux/flutter/ephemeral/flutter_linux/fl_engine.h, /home/<USER>/HVAC/FlutterHVAC-MD3/hvac_flutter_md3/linux/flutter/ephemeral/flutter_linux/fl_json_message_codec.h, /home/<USER>/HVAC/FlutterHVAC-MD3/hvac_flutter_md3/linux/flutter/ephemeral/flutter_linux/fl_json_method_codec.h, /home/<USER>/HVAC/FlutterHVAC-MD3/hvac_flutter_md3/linux/flutter/ephemeral/flutter_linux/fl_message_codec.h, /home/<USER>/HVAC/FlutterHVAC-MD3/hvac_flutter_md3/linux/flutter/ephemeral/flutter_linux/fl_method_call.h, /home/<USER>/HVAC/FlutterHVAC-MD3/hvac_flutter_md3/linux/flutter/ephemeral/flutter_linux/fl_method_channel.h, /home/<USER>/HVAC/FlutterHVAC-MD3/hvac_flutter_md3/linux/flutter/ephemeral/flutter_linux/fl_method_codec.h, /home/<USER>/HVAC/FlutterHVAC-MD3/hvac_flutter_md3/linux/flutter/ephemeral/flutter_linux/fl_method_response.h, /home/<USER>/HVAC/FlutterHVAC-MD3/hvac_flutter_md3/linux/flutter/ephemeral/flutter_linux/fl_plugin_registrar.h, /home/<USER>/HVAC/FlutterHVAC-MD3/hvac_flutter_md3/linux/flutter/ephemeral/flutter_linux/fl_plugin_registry.h, /home/<USER>/HVAC/FlutterHVAC-MD3/hvac_flutter_md3/linux/flutter/ephemeral/flutter_linux/fl_standard_message_codec.h, /home/<USER>/HVAC/FlutterHVAC-MD3/hvac_flutter_md3/linux/flutter/ephemeral/flutter_linux/fl_standard_method_codec.h, /home/<USER>/HVAC/FlutterHVAC-MD3/hvac_flutter_md3/linux/flutter/ephemeral/flutter_linux/fl_string_codec.h, /home/<USER>/HVAC/FlutterHVAC-MD3/hvac_flutter_md3/linux/flutter/ephemeral/flutter_linux/fl_value.h, /home/<USER>/HVAC/FlutterHVAC-MD3/hvac_flutter_md3/linux/flutter/ephemeral/flutter_linux/fl_view.h, /home/<USER>/HVAC/FlutterHVAC-MD3/hvac_flutter_md3/linux/flutter/ephemeral/flutter_linux/flutter_linux.h, _phony_
  restat = 1

# =============================================================================
# Write statements declared in CMakeLists.txt:
# /home/<USER>/HVAC/FlutterHVAC-MD3/hvac_flutter_md3/linux/CMakeLists.txt
# =============================================================================


#############################################
# Utility command for install/strip

build runner/CMakeFiles/install/strip.util: CUSTOM_COMMAND runner/all
  COMMAND = cd /home/<USER>/HVAC/FlutterHVAC-MD3/hvac_flutter_md3/build/linux/x64/debug/runner && /snap/flutter/149/usr/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
  DESC = Installing the project stripped...
  pool = console
  restat = 1

build runner/install/strip: phony runner/CMakeFiles/install/strip.util


#############################################
# Utility command for install/local

build runner/CMakeFiles/install/local.util: CUSTOM_COMMAND runner/all
  COMMAND = cd /home/<USER>/HVAC/FlutterHVAC-MD3/hvac_flutter_md3/build/linux/x64/debug/runner && /snap/flutter/149/usr/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
  DESC = Installing only the local directory...
  pool = console
  restat = 1

build runner/install/local: phony runner/CMakeFiles/install/local.util


#############################################
# Utility command for edit_cache

build runner/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cd /home/<USER>/HVAC/FlutterHVAC-MD3/hvac_flutter_md3/build/linux/x64/debug/runner && /snap/flutter/149/usr/bin/cmake -E echo No\ interactive\ CMake\ dialog\ available.
  DESC = No interactive CMake dialog available...
  restat = 1

build runner/edit_cache: phony runner/CMakeFiles/edit_cache.util


#############################################
# Utility command for install

build runner/CMakeFiles/install.util: CUSTOM_COMMAND runner/all
  COMMAND = cd /home/<USER>/HVAC/FlutterHVAC-MD3/hvac_flutter_md3/build/linux/x64/debug/runner && /snap/flutter/149/usr/bin/cmake -P cmake_install.cmake
  DESC = Install the project...
  pool = console
  restat = 1

build runner/install: phony runner/CMakeFiles/install.util


#############################################
# Utility command for list_install_components

build runner/list_install_components: phony


#############################################
# Utility command for rebuild_cache

build runner/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cd /home/<USER>/HVAC/FlutterHVAC-MD3/hvac_flutter_md3/build/linux/x64/debug/runner && /snap/flutter/149/usr/bin/cmake -S/home/<USER>/HVAC/FlutterHVAC-MD3/hvac_flutter_md3/linux -B/home/<USER>/HVAC/FlutterHVAC-MD3/hvac_flutter_md3/build/linux/x64/debug
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build runner/rebuild_cache: phony runner/CMakeFiles/rebuild_cache.util

# =============================================================================
# Object build statements for EXECUTABLE target hvac_flutter_md3


#############################################
# Order-only phony target for hvac_flutter_md3

build cmake_object_order_depends_target_hvac_flutter_md3: phony || cmake_object_order_depends_target_dynamic_color_plugin cmake_object_order_depends_target_file_selector_linux_plugin cmake_object_order_depends_target_flutter_secure_storage_linux_plugin cmake_object_order_depends_target_url_launcher_linux_plugin flutter/flutter_assemble

build runner/CMakeFiles/hvac_flutter_md3.dir/main.cc.o: CXX_COMPILER__hvac_flutter_md3 /home/<USER>/HVAC/FlutterHVAC-MD3/hvac_flutter_md3/linux/runner/main.cc || cmake_object_order_depends_target_hvac_flutter_md3
  DEFINES = -DAPPLICATION_ID=\"com.example.hvac_flutter_md3\"
  DEP_FILE = runner/CMakeFiles/hvac_flutter_md3.dir/main.cc.o.d
  FLAGS = -g   -Wall -Werror -pthread
  INCLUDES = -I/home/<USER>/HVAC/FlutterHVAC-MD3/hvac_flutter_md3/linux -I/home/<USER>/HVAC/FlutterHVAC-MD3/hvac_flutter_md3/linux/flutter/ephemeral -I/home/<USER>/HVAC/FlutterHVAC-MD3/hvac_flutter_md3/linux/flutter/ephemeral/.plugin_symlinks/dynamic_color/linux/include -I/home/<USER>/HVAC/FlutterHVAC-MD3/hvac_flutter_md3/linux/flutter/ephemeral/.plugin_symlinks/file_selector_linux/linux/include -I/home/<USER>/HVAC/FlutterHVAC-MD3/hvac_flutter_md3/linux/flutter/ephemeral/.plugin_symlinks/flutter_secure_storage_linux/linux/include -I/home/<USER>/HVAC/FlutterHVAC-MD3/hvac_flutter_md3/linux/flutter/ephemeral/.plugin_symlinks/url_launcher_linux/linux/include -isystem /snap/flutter/current/usr/include/gtk-3.0 -isystem /snap/flutter/current/usr/include/at-spi2-atk/2.0 -isystem /snap/flutter/current/usr/include/at-spi-2.0 -isystem /snap/flutter/current/usr/include/dbus-1.0 -isystem /snap/flutter/current/usr/lib/x86_64-linux-gnu/dbus-1.0/include -isystem /snap/flutter/current/usr/include/gio-unix-2.0 -isystem /snap/flutter/current/usr/include/cairo -isystem /snap/flutter/current/usr/include/pango-1.0 -isystem /snap/flutter/current/usr/include/fribidi -isystem /snap/flutter/current/usr/include/harfbuzz -isystem /snap/flutter/current/usr/include/atk-1.0 -isystem /snap/flutter/current/usr/include/pixman-1 -isystem /snap/flutter/current/usr/include/uuid -isystem /snap/flutter/current/usr/include/freetype2 -isystem /snap/flutter/current/usr/include/libpng16 -isystem /snap/flutter/current/usr/include/gdk-pixbuf-2.0 -isystem /snap/flutter/current/usr/include/libmount -isystem /snap/flutter/current/usr/include/blkid -isystem /snap/flutter/current/usr/include/glib-2.0 -isystem /snap/flutter/current/usr/lib/x86_64-linux-gnu/glib-2.0/include
  OBJECT_DIR = runner/CMakeFiles/hvac_flutter_md3.dir
  OBJECT_FILE_DIR = runner/CMakeFiles/hvac_flutter_md3.dir

build runner/CMakeFiles/hvac_flutter_md3.dir/my_application.cc.o: CXX_COMPILER__hvac_flutter_md3 /home/<USER>/HVAC/FlutterHVAC-MD3/hvac_flutter_md3/linux/runner/my_application.cc || cmake_object_order_depends_target_hvac_flutter_md3
  DEFINES = -DAPPLICATION_ID=\"com.example.hvac_flutter_md3\"
  DEP_FILE = runner/CMakeFiles/hvac_flutter_md3.dir/my_application.cc.o.d
  FLAGS = -g   -Wall -Werror -pthread
  INCLUDES = -I/home/<USER>/HVAC/FlutterHVAC-MD3/hvac_flutter_md3/linux -I/home/<USER>/HVAC/FlutterHVAC-MD3/hvac_flutter_md3/linux/flutter/ephemeral -I/home/<USER>/HVAC/FlutterHVAC-MD3/hvac_flutter_md3/linux/flutter/ephemeral/.plugin_symlinks/dynamic_color/linux/include -I/home/<USER>/HVAC/FlutterHVAC-MD3/hvac_flutter_md3/linux/flutter/ephemeral/.plugin_symlinks/file_selector_linux/linux/include -I/home/<USER>/HVAC/FlutterHVAC-MD3/hvac_flutter_md3/linux/flutter/ephemeral/.plugin_symlinks/flutter_secure_storage_linux/linux/include -I/home/<USER>/HVAC/FlutterHVAC-MD3/hvac_flutter_md3/linux/flutter/ephemeral/.plugin_symlinks/url_launcher_linux/linux/include -isystem /snap/flutter/current/usr/include/gtk-3.0 -isystem /snap/flutter/current/usr/include/at-spi2-atk/2.0 -isystem /snap/flutter/current/usr/include/at-spi-2.0 -isystem /snap/flutter/current/usr/include/dbus-1.0 -isystem /snap/flutter/current/usr/lib/x86_64-linux-gnu/dbus-1.0/include -isystem /snap/flutter/current/usr/include/gio-unix-2.0 -isystem /snap/flutter/current/usr/include/cairo -isystem /snap/flutter/current/usr/include/pango-1.0 -isystem /snap/flutter/current/usr/include/fribidi -isystem /snap/flutter/current/usr/include/harfbuzz -isystem /snap/flutter/current/usr/include/atk-1.0 -isystem /snap/flutter/current/usr/include/pixman-1 -isystem /snap/flutter/current/usr/include/uuid -isystem /snap/flutter/current/usr/include/freetype2 -isystem /snap/flutter/current/usr/include/libpng16 -isystem /snap/flutter/current/usr/include/gdk-pixbuf-2.0 -isystem /snap/flutter/current/usr/include/libmount -isystem /snap/flutter/current/usr/include/blkid -isystem /snap/flutter/current/usr/include/glib-2.0 -isystem /snap/flutter/current/usr/lib/x86_64-linux-gnu/glib-2.0/include
  OBJECT_DIR = runner/CMakeFiles/hvac_flutter_md3.dir
  OBJECT_FILE_DIR = runner/CMakeFiles/hvac_flutter_md3.dir

build runner/CMakeFiles/hvac_flutter_md3.dir/__/flutter/generated_plugin_registrant.cc.o: CXX_COMPILER__hvac_flutter_md3 /home/<USER>/HVAC/FlutterHVAC-MD3/hvac_flutter_md3/linux/flutter/generated_plugin_registrant.cc || cmake_object_order_depends_target_hvac_flutter_md3
  DEFINES = -DAPPLICATION_ID=\"com.example.hvac_flutter_md3\"
  DEP_FILE = runner/CMakeFiles/hvac_flutter_md3.dir/__/flutter/generated_plugin_registrant.cc.o.d
  FLAGS = -g   -Wall -Werror -pthread
  INCLUDES = -I/home/<USER>/HVAC/FlutterHVAC-MD3/hvac_flutter_md3/linux -I/home/<USER>/HVAC/FlutterHVAC-MD3/hvac_flutter_md3/linux/flutter/ephemeral -I/home/<USER>/HVAC/FlutterHVAC-MD3/hvac_flutter_md3/linux/flutter/ephemeral/.plugin_symlinks/dynamic_color/linux/include -I/home/<USER>/HVAC/FlutterHVAC-MD3/hvac_flutter_md3/linux/flutter/ephemeral/.plugin_symlinks/file_selector_linux/linux/include -I/home/<USER>/HVAC/FlutterHVAC-MD3/hvac_flutter_md3/linux/flutter/ephemeral/.plugin_symlinks/flutter_secure_storage_linux/linux/include -I/home/<USER>/HVAC/FlutterHVAC-MD3/hvac_flutter_md3/linux/flutter/ephemeral/.plugin_symlinks/url_launcher_linux/linux/include -isystem /snap/flutter/current/usr/include/gtk-3.0 -isystem /snap/flutter/current/usr/include/at-spi2-atk/2.0 -isystem /snap/flutter/current/usr/include/at-spi-2.0 -isystem /snap/flutter/current/usr/include/dbus-1.0 -isystem /snap/flutter/current/usr/lib/x86_64-linux-gnu/dbus-1.0/include -isystem /snap/flutter/current/usr/include/gio-unix-2.0 -isystem /snap/flutter/current/usr/include/cairo -isystem /snap/flutter/current/usr/include/pango-1.0 -isystem /snap/flutter/current/usr/include/fribidi -isystem /snap/flutter/current/usr/include/harfbuzz -isystem /snap/flutter/current/usr/include/atk-1.0 -isystem /snap/flutter/current/usr/include/pixman-1 -isystem /snap/flutter/current/usr/include/uuid -isystem /snap/flutter/current/usr/include/freetype2 -isystem /snap/flutter/current/usr/include/libpng16 -isystem /snap/flutter/current/usr/include/gdk-pixbuf-2.0 -isystem /snap/flutter/current/usr/include/libmount -isystem /snap/flutter/current/usr/include/blkid -isystem /snap/flutter/current/usr/include/glib-2.0 -isystem /snap/flutter/current/usr/lib/x86_64-linux-gnu/glib-2.0/include
  OBJECT_DIR = runner/CMakeFiles/hvac_flutter_md3.dir
  OBJECT_FILE_DIR = runner/CMakeFiles/hvac_flutter_md3.dir/__/flutter


# =============================================================================
# Link build statements for EXECUTABLE target hvac_flutter_md3


#############################################
# Link the executable intermediates_do_not_run/hvac_flutter_md3

build intermediates_do_not_run/hvac_flutter_md3: CXX_EXECUTABLE_LINKER__hvac_flutter_md3 runner/CMakeFiles/hvac_flutter_md3.dir/main.cc.o runner/CMakeFiles/hvac_flutter_md3.dir/my_application.cc.o runner/CMakeFiles/hvac_flutter_md3.dir/__/flutter/generated_plugin_registrant.cc.o | plugins/dynamic_color/libdynamic_color_plugin.so plugins/file_selector_linux/libfile_selector_linux_plugin.so plugins/flutter_secure_storage_linux/libflutter_secure_storage_linux_plugin.so plugins/url_launcher_linux/liburl_launcher_linux_plugin.so /home/<USER>/HVAC/FlutterHVAC-MD3/hvac_flutter_md3/linux/flutter/ephemeral/libflutter_linux_gtk.so /snap/flutter/current/usr/lib/x86_64-linux-gnu/libgtk-3.so /snap/flutter/current/usr/lib/x86_64-linux-gnu/libgdk-3.so /snap/flutter/current/usr/lib/x86_64-linux-gnu/libpangocairo-1.0.so /snap/flutter/current/usr/lib/x86_64-linux-gnu/libpango-1.0.so /snap/flutter/current/usr/lib/x86_64-linux-gnu/libharfbuzz.so /snap/flutter/current/usr/lib/x86_64-linux-gnu/libatk-1.0.so /snap/flutter/current/usr/lib/x86_64-linux-gnu/libcairo-gobject.so /snap/flutter/current/usr/lib/x86_64-linux-gnu/libcairo.so /snap/flutter/current/usr/lib/x86_64-linux-gnu/libgdk_pixbuf-2.0.so /snap/flutter/current/usr/lib/x86_64-linux-gnu/libgio-2.0.so /snap/flutter/current/usr/lib/x86_64-linux-gnu/libgobject-2.0.so /snap/flutter/current/usr/lib/x86_64-linux-gnu/libglib-2.0.so || flutter/flutter_assemble plugins/dynamic_color/libdynamic_color_plugin.so plugins/file_selector_linux/libfile_selector_linux_plugin.so plugins/flutter_secure_storage_linux/libflutter_secure_storage_linux_plugin.so plugins/url_launcher_linux/liburl_launcher_linux_plugin.so
  FLAGS = -g
  LINK_FLAGS = -B/snap/flutter/current/usr/lib/gcc/x86_64-linux-gnu/9 -B/snap/flutter/current/usr/lib/x86_64-linux-gnu -B/snap/flutter/current/lib/x86_64-linux-gnu -B/snap/flutter/current/usr/lib/ -L/snap/flutter/current/usr/lib/gcc/x86_64-linux-gnu/9 -L/snap/flutter/current/usr/lib/x86_64-linux-gnu -L/snap/flutter/current/lib/x86_64-linux-gnu -L/snap/flutter/current/usr/lib/ -lblkid -lgcrypt -llzma -llz4 -lgpg-error -luuid -lpthread -ldl -lepoxy -lfontconfig -B/snap/flutter/current/usr/lib/gcc/x86_64-linux-gnu/9 -B/snap/flutter/current/usr/lib/x86_64-linux-gnu -B/snap/flutter/current/lib/x86_64-linux-gnu -B/snap/flutter/current/usr/lib/ -L/snap/flutter/current/usr/lib/gcc/x86_64-linux-gnu/9 -L/snap/flutter/current/usr/lib/x86_64-linux-gnu -L/snap/flutter/current/lib/x86_64-linux-gnu -L/snap/flutter/current/usr/lib/ -lblkid -lgcrypt -llzma -llz4 -lgpg-error -luuid -lpthread -ldl -lepoxy -lfontconfig
  LINK_LIBRARIES = -Wl,-rpath,/home/<USER>/HVAC/FlutterHVAC-MD3/hvac_flutter_md3/build/linux/x64/debug/plugins/dynamic_color:/home/<USER>/HVAC/FlutterHVAC-MD3/hvac_flutter_md3/build/linux/x64/debug/plugins/file_selector_linux:/home/<USER>/HVAC/FlutterHVAC-MD3/hvac_flutter_md3/build/linux/x64/debug/plugins/flutter_secure_storage_linux:/home/<USER>/HVAC/FlutterHVAC-MD3/hvac_flutter_md3/build/linux/x64/debug/plugins/url_launcher_linux:/home/<USER>/HVAC/FlutterHVAC-MD3/hvac_flutter_md3/linux/flutter/ephemeral:  plugins/dynamic_color/libdynamic_color_plugin.so  plugins/file_selector_linux/libfile_selector_linux_plugin.so  plugins/flutter_secure_storage_linux/libflutter_secure_storage_linux_plugin.so  plugins/url_launcher_linux/liburl_launcher_linux_plugin.so  /home/<USER>/HVAC/FlutterHVAC-MD3/hvac_flutter_md3/linux/flutter/ephemeral/libflutter_linux_gtk.so  /snap/flutter/current/usr/lib/x86_64-linux-gnu/libgtk-3.so  /snap/flutter/current/usr/lib/x86_64-linux-gnu/libgdk-3.so  /snap/flutter/current/usr/lib/x86_64-linux-gnu/libpangocairo-1.0.so  /snap/flutter/current/usr/lib/x86_64-linux-gnu/libpango-1.0.so  /snap/flutter/current/usr/lib/x86_64-linux-gnu/libharfbuzz.so  /snap/flutter/current/usr/lib/x86_64-linux-gnu/libatk-1.0.so  /snap/flutter/current/usr/lib/x86_64-linux-gnu/libcairo-gobject.so  /snap/flutter/current/usr/lib/x86_64-linux-gnu/libcairo.so  /snap/flutter/current/usr/lib/x86_64-linux-gnu/libgdk_pixbuf-2.0.so  /snap/flutter/current/usr/lib/x86_64-linux-gnu/libgio-2.0.so  /snap/flutter/current/usr/lib/x86_64-linux-gnu/libgobject-2.0.so  /snap/flutter/current/usr/lib/x86_64-linux-gnu/libglib-2.0.so
  OBJECT_DIR = runner/CMakeFiles/hvac_flutter_md3.dir
  POST_BUILD = :
  PRE_LINK = :
  TARGET_FILE = intermediates_do_not_run/hvac_flutter_md3
  TARGET_PDB = hvac_flutter_md3.dbg

# =============================================================================
# Write statements declared in CMakeLists.txt:
# /home/<USER>/HVAC/FlutterHVAC-MD3/hvac_flutter_md3/linux/flutter/generated_plugins.cmake
# =============================================================================


#############################################
# Utility command for install/strip

build plugins/dynamic_color/CMakeFiles/install/strip.util: CUSTOM_COMMAND plugins/dynamic_color/all
  COMMAND = cd /home/<USER>/HVAC/FlutterHVAC-MD3/hvac_flutter_md3/build/linux/x64/debug/plugins/dynamic_color && /snap/flutter/149/usr/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
  DESC = Installing the project stripped...
  pool = console
  restat = 1

build plugins/dynamic_color/install/strip: phony plugins/dynamic_color/CMakeFiles/install/strip.util


#############################################
# Utility command for install/local

build plugins/dynamic_color/CMakeFiles/install/local.util: CUSTOM_COMMAND plugins/dynamic_color/all
  COMMAND = cd /home/<USER>/HVAC/FlutterHVAC-MD3/hvac_flutter_md3/build/linux/x64/debug/plugins/dynamic_color && /snap/flutter/149/usr/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
  DESC = Installing only the local directory...
  pool = console
  restat = 1

build plugins/dynamic_color/install/local: phony plugins/dynamic_color/CMakeFiles/install/local.util


#############################################
# Utility command for install

build plugins/dynamic_color/CMakeFiles/install.util: CUSTOM_COMMAND plugins/dynamic_color/all
  COMMAND = cd /home/<USER>/HVAC/FlutterHVAC-MD3/hvac_flutter_md3/build/linux/x64/debug/plugins/dynamic_color && /snap/flutter/149/usr/bin/cmake -P cmake_install.cmake
  DESC = Install the project...
  pool = console
  restat = 1

build plugins/dynamic_color/install: phony plugins/dynamic_color/CMakeFiles/install.util


#############################################
# Utility command for list_install_components

build plugins/dynamic_color/list_install_components: phony


#############################################
# Utility command for rebuild_cache

build plugins/dynamic_color/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cd /home/<USER>/HVAC/FlutterHVAC-MD3/hvac_flutter_md3/build/linux/x64/debug/plugins/dynamic_color && /snap/flutter/149/usr/bin/cmake -S/home/<USER>/HVAC/FlutterHVAC-MD3/hvac_flutter_md3/linux -B/home/<USER>/HVAC/FlutterHVAC-MD3/hvac_flutter_md3/build/linux/x64/debug
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build plugins/dynamic_color/rebuild_cache: phony plugins/dynamic_color/CMakeFiles/rebuild_cache.util


#############################################
# Utility command for edit_cache

build plugins/dynamic_color/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cd /home/<USER>/HVAC/FlutterHVAC-MD3/hvac_flutter_md3/build/linux/x64/debug/plugins/dynamic_color && /snap/flutter/149/usr/bin/cmake -E echo No\ interactive\ CMake\ dialog\ available.
  DESC = No interactive CMake dialog available...
  restat = 1

build plugins/dynamic_color/edit_cache: phony plugins/dynamic_color/CMakeFiles/edit_cache.util

# =============================================================================
# Object build statements for SHARED_LIBRARY target dynamic_color_plugin


#############################################
# Order-only phony target for dynamic_color_plugin

build cmake_object_order_depends_target_dynamic_color_plugin: phony || flutter/flutter_assemble

build plugins/dynamic_color/CMakeFiles/dynamic_color_plugin.dir/dynamic_color_plugin.cc.o: CXX_COMPILER__dynamic_color_plugin /home/<USER>/HVAC/FlutterHVAC-MD3/hvac_flutter_md3/linux/flutter/ephemeral/.plugin_symlinks/dynamic_color/linux/dynamic_color_plugin.cc || cmake_object_order_depends_target_dynamic_color_plugin
  DEFINES = -DFLUTTER_PLUGIN_IMPL -Ddynamic_color_plugin_EXPORTS
  DEP_FILE = plugins/dynamic_color/CMakeFiles/dynamic_color_plugin.dir/dynamic_color_plugin.cc.o.d
  FLAGS = -g -fPIC -fvisibility=hidden   -Wall -Werror -pthread
  INCLUDES = -I/home/<USER>/HVAC/FlutterHVAC-MD3/hvac_flutter_md3/linux/flutter/ephemeral -isystem /snap/flutter/current/usr/include/gtk-3.0 -isystem /snap/flutter/current/usr/include/at-spi2-atk/2.0 -isystem /snap/flutter/current/usr/include/at-spi-2.0 -isystem /snap/flutter/current/usr/include/dbus-1.0 -isystem /snap/flutter/current/usr/lib/x86_64-linux-gnu/dbus-1.0/include -isystem /snap/flutter/current/usr/include/gio-unix-2.0 -isystem /snap/flutter/current/usr/include/cairo -isystem /snap/flutter/current/usr/include/pango-1.0 -isystem /snap/flutter/current/usr/include/fribidi -isystem /snap/flutter/current/usr/include/harfbuzz -isystem /snap/flutter/current/usr/include/atk-1.0 -isystem /snap/flutter/current/usr/include/pixman-1 -isystem /snap/flutter/current/usr/include/uuid -isystem /snap/flutter/current/usr/include/freetype2 -isystem /snap/flutter/current/usr/include/libpng16 -isystem /snap/flutter/current/usr/include/gdk-pixbuf-2.0 -isystem /snap/flutter/current/usr/include/libmount -isystem /snap/flutter/current/usr/include/blkid -isystem /snap/flutter/current/usr/include/glib-2.0 -isystem /snap/flutter/current/usr/lib/x86_64-linux-gnu/glib-2.0/include
  OBJECT_DIR = plugins/dynamic_color/CMakeFiles/dynamic_color_plugin.dir
  OBJECT_FILE_DIR = plugins/dynamic_color/CMakeFiles/dynamic_color_plugin.dir


# =============================================================================
# Link build statements for SHARED_LIBRARY target dynamic_color_plugin


#############################################
# Link the shared library plugins/dynamic_color/libdynamic_color_plugin.so

build plugins/dynamic_color/libdynamic_color_plugin.so: CXX_SHARED_LIBRARY_LINKER__dynamic_color_plugin plugins/dynamic_color/CMakeFiles/dynamic_color_plugin.dir/dynamic_color_plugin.cc.o | /home/<USER>/HVAC/FlutterHVAC-MD3/hvac_flutter_md3/linux/flutter/ephemeral/libflutter_linux_gtk.so /snap/flutter/current/usr/lib/x86_64-linux-gnu/libgtk-3.so /snap/flutter/current/usr/lib/x86_64-linux-gnu/libgdk-3.so /snap/flutter/current/usr/lib/x86_64-linux-gnu/libpangocairo-1.0.so /snap/flutter/current/usr/lib/x86_64-linux-gnu/libpango-1.0.so /snap/flutter/current/usr/lib/x86_64-linux-gnu/libharfbuzz.so /snap/flutter/current/usr/lib/x86_64-linux-gnu/libatk-1.0.so /snap/flutter/current/usr/lib/x86_64-linux-gnu/libcairo-gobject.so /snap/flutter/current/usr/lib/x86_64-linux-gnu/libcairo.so /snap/flutter/current/usr/lib/x86_64-linux-gnu/libgdk_pixbuf-2.0.so /snap/flutter/current/usr/lib/x86_64-linux-gnu/libgio-2.0.so /snap/flutter/current/usr/lib/x86_64-linux-gnu/libgobject-2.0.so /snap/flutter/current/usr/lib/x86_64-linux-gnu/libglib-2.0.so || flutter/flutter_assemble
  LANGUAGE_COMPILE_FLAGS = -g
  LINK_FLAGS = -B/snap/flutter/current/usr/lib/gcc/x86_64-linux-gnu/9 -B/snap/flutter/current/usr/lib/x86_64-linux-gnu -B/snap/flutter/current/lib/x86_64-linux-gnu -B/snap/flutter/current/usr/lib/ -L/snap/flutter/current/usr/lib/gcc/x86_64-linux-gnu/9 -L/snap/flutter/current/usr/lib/x86_64-linux-gnu -L/snap/flutter/current/lib/x86_64-linux-gnu -L/snap/flutter/current/usr/lib/ -lblkid -lgcrypt -llzma -llz4 -lgpg-error -luuid -lpthread -ldl -lepoxy -lfontconfig -B/snap/flutter/current/usr/lib/gcc/x86_64-linux-gnu/9 -B/snap/flutter/current/usr/lib/x86_64-linux-gnu -B/snap/flutter/current/lib/x86_64-linux-gnu -B/snap/flutter/current/usr/lib/ -L/snap/flutter/current/usr/lib/gcc/x86_64-linux-gnu/9 -L/snap/flutter/current/usr/lib/x86_64-linux-gnu -L/snap/flutter/current/lib/x86_64-linux-gnu -L/snap/flutter/current/usr/lib/ -lblkid -lgcrypt -llzma -llz4 -lgpg-error -luuid -lpthread -ldl -lepoxy -lfontconfig
  LINK_LIBRARIES = -Wl,-rpath,/home/<USER>/HVAC/FlutterHVAC-MD3/hvac_flutter_md3/linux/flutter/ephemeral  /home/<USER>/HVAC/FlutterHVAC-MD3/hvac_flutter_md3/linux/flutter/ephemeral/libflutter_linux_gtk.so  /snap/flutter/current/usr/lib/x86_64-linux-gnu/libgtk-3.so  /snap/flutter/current/usr/lib/x86_64-linux-gnu/libgdk-3.so  /snap/flutter/current/usr/lib/x86_64-linux-gnu/libpangocairo-1.0.so  /snap/flutter/current/usr/lib/x86_64-linux-gnu/libpango-1.0.so  /snap/flutter/current/usr/lib/x86_64-linux-gnu/libharfbuzz.so  /snap/flutter/current/usr/lib/x86_64-linux-gnu/libatk-1.0.so  /snap/flutter/current/usr/lib/x86_64-linux-gnu/libcairo-gobject.so  /snap/flutter/current/usr/lib/x86_64-linux-gnu/libcairo.so  /snap/flutter/current/usr/lib/x86_64-linux-gnu/libgdk_pixbuf-2.0.so  /snap/flutter/current/usr/lib/x86_64-linux-gnu/libgio-2.0.so  /snap/flutter/current/usr/lib/x86_64-linux-gnu/libgobject-2.0.so  /snap/flutter/current/usr/lib/x86_64-linux-gnu/libglib-2.0.so
  OBJECT_DIR = plugins/dynamic_color/CMakeFiles/dynamic_color_plugin.dir
  POST_BUILD = :
  PRE_LINK = :
  SONAME = libdynamic_color_plugin.so
  SONAME_FLAG = -Wl,-soname,
  TARGET_FILE = plugins/dynamic_color/libdynamic_color_plugin.so
  TARGET_PDB = dynamic_color_plugin.so.dbg

# =============================================================================
# Write statements declared in CMakeLists.txt:
# /home/<USER>/HVAC/FlutterHVAC-MD3/hvac_flutter_md3/linux/flutter/generated_plugins.cmake
# =============================================================================


#############################################
# Utility command for install/strip

build plugins/file_selector_linux/CMakeFiles/install/strip.util: CUSTOM_COMMAND plugins/file_selector_linux/all
  COMMAND = cd /home/<USER>/HVAC/FlutterHVAC-MD3/hvac_flutter_md3/build/linux/x64/debug/plugins/file_selector_linux && /snap/flutter/149/usr/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
  DESC = Installing the project stripped...
  pool = console
  restat = 1

build plugins/file_selector_linux/install/strip: phony plugins/file_selector_linux/CMakeFiles/install/strip.util


#############################################
# Utility command for install/local

build plugins/file_selector_linux/CMakeFiles/install/local.util: CUSTOM_COMMAND plugins/file_selector_linux/all
  COMMAND = cd /home/<USER>/HVAC/FlutterHVAC-MD3/hvac_flutter_md3/build/linux/x64/debug/plugins/file_selector_linux && /snap/flutter/149/usr/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
  DESC = Installing only the local directory...
  pool = console
  restat = 1

build plugins/file_selector_linux/install/local: phony plugins/file_selector_linux/CMakeFiles/install/local.util


#############################################
# Utility command for install

build plugins/file_selector_linux/CMakeFiles/install.util: CUSTOM_COMMAND plugins/file_selector_linux/all
  COMMAND = cd /home/<USER>/HVAC/FlutterHVAC-MD3/hvac_flutter_md3/build/linux/x64/debug/plugins/file_selector_linux && /snap/flutter/149/usr/bin/cmake -P cmake_install.cmake
  DESC = Install the project...
  pool = console
  restat = 1

build plugins/file_selector_linux/install: phony plugins/file_selector_linux/CMakeFiles/install.util


#############################################
# Utility command for list_install_components

build plugins/file_selector_linux/list_install_components: phony


#############################################
# Utility command for rebuild_cache

build plugins/file_selector_linux/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cd /home/<USER>/HVAC/FlutterHVAC-MD3/hvac_flutter_md3/build/linux/x64/debug/plugins/file_selector_linux && /snap/flutter/149/usr/bin/cmake -S/home/<USER>/HVAC/FlutterHVAC-MD3/hvac_flutter_md3/linux -B/home/<USER>/HVAC/FlutterHVAC-MD3/hvac_flutter_md3/build/linux/x64/debug
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build plugins/file_selector_linux/rebuild_cache: phony plugins/file_selector_linux/CMakeFiles/rebuild_cache.util


#############################################
# Utility command for edit_cache

build plugins/file_selector_linux/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cd /home/<USER>/HVAC/FlutterHVAC-MD3/hvac_flutter_md3/build/linux/x64/debug/plugins/file_selector_linux && /snap/flutter/149/usr/bin/cmake -E echo No\ interactive\ CMake\ dialog\ available.
  DESC = No interactive CMake dialog available...
  restat = 1

build plugins/file_selector_linux/edit_cache: phony plugins/file_selector_linux/CMakeFiles/edit_cache.util

# =============================================================================
# Object build statements for SHARED_LIBRARY target file_selector_linux_plugin


#############################################
# Order-only phony target for file_selector_linux_plugin

build cmake_object_order_depends_target_file_selector_linux_plugin: phony || flutter/flutter_assemble

build plugins/file_selector_linux/CMakeFiles/file_selector_linux_plugin.dir/file_selector_plugin.cc.o: CXX_COMPILER__file_selector_linux_plugin /home/<USER>/HVAC/FlutterHVAC-MD3/hvac_flutter_md3/linux/flutter/ephemeral/.plugin_symlinks/file_selector_linux/linux/file_selector_plugin.cc || cmake_object_order_depends_target_file_selector_linux_plugin
  DEFINES = -DFLUTTER_PLUGIN_IMPL -Dfile_selector_linux_plugin_EXPORTS
  DEP_FILE = plugins/file_selector_linux/CMakeFiles/file_selector_linux_plugin.dir/file_selector_plugin.cc.o.d
  FLAGS = -g -fPIC -fvisibility=hidden   -Wall -Werror -pthread
  INCLUDES = -I/home/<USER>/HVAC/FlutterHVAC-MD3/hvac_flutter_md3/linux/flutter/ephemeral -isystem /snap/flutter/current/usr/include/gtk-3.0 -isystem /snap/flutter/current/usr/include/at-spi2-atk/2.0 -isystem /snap/flutter/current/usr/include/at-spi-2.0 -isystem /snap/flutter/current/usr/include/dbus-1.0 -isystem /snap/flutter/current/usr/lib/x86_64-linux-gnu/dbus-1.0/include -isystem /snap/flutter/current/usr/include/gio-unix-2.0 -isystem /snap/flutter/current/usr/include/cairo -isystem /snap/flutter/current/usr/include/pango-1.0 -isystem /snap/flutter/current/usr/include/fribidi -isystem /snap/flutter/current/usr/include/harfbuzz -isystem /snap/flutter/current/usr/include/atk-1.0 -isystem /snap/flutter/current/usr/include/pixman-1 -isystem /snap/flutter/current/usr/include/uuid -isystem /snap/flutter/current/usr/include/freetype2 -isystem /snap/flutter/current/usr/include/libpng16 -isystem /snap/flutter/current/usr/include/gdk-pixbuf-2.0 -isystem /snap/flutter/current/usr/include/libmount -isystem /snap/flutter/current/usr/include/blkid -isystem /snap/flutter/current/usr/include/glib-2.0 -isystem /snap/flutter/current/usr/lib/x86_64-linux-gnu/glib-2.0/include
  OBJECT_DIR = plugins/file_selector_linux/CMakeFiles/file_selector_linux_plugin.dir
  OBJECT_FILE_DIR = plugins/file_selector_linux/CMakeFiles/file_selector_linux_plugin.dir

build plugins/file_selector_linux/CMakeFiles/file_selector_linux_plugin.dir/messages.g.cc.o: CXX_COMPILER__file_selector_linux_plugin /home/<USER>/HVAC/FlutterHVAC-MD3/hvac_flutter_md3/linux/flutter/ephemeral/.plugin_symlinks/file_selector_linux/linux/messages.g.cc || cmake_object_order_depends_target_file_selector_linux_plugin
  DEFINES = -DFLUTTER_PLUGIN_IMPL -Dfile_selector_linux_plugin_EXPORTS
  DEP_FILE = plugins/file_selector_linux/CMakeFiles/file_selector_linux_plugin.dir/messages.g.cc.o.d
  FLAGS = -g -fPIC -fvisibility=hidden   -Wall -Werror -pthread
  INCLUDES = -I/home/<USER>/HVAC/FlutterHVAC-MD3/hvac_flutter_md3/linux/flutter/ephemeral -isystem /snap/flutter/current/usr/include/gtk-3.0 -isystem /snap/flutter/current/usr/include/at-spi2-atk/2.0 -isystem /snap/flutter/current/usr/include/at-spi-2.0 -isystem /snap/flutter/current/usr/include/dbus-1.0 -isystem /snap/flutter/current/usr/lib/x86_64-linux-gnu/dbus-1.0/include -isystem /snap/flutter/current/usr/include/gio-unix-2.0 -isystem /snap/flutter/current/usr/include/cairo -isystem /snap/flutter/current/usr/include/pango-1.0 -isystem /snap/flutter/current/usr/include/fribidi -isystem /snap/flutter/current/usr/include/harfbuzz -isystem /snap/flutter/current/usr/include/atk-1.0 -isystem /snap/flutter/current/usr/include/pixman-1 -isystem /snap/flutter/current/usr/include/uuid -isystem /snap/flutter/current/usr/include/freetype2 -isystem /snap/flutter/current/usr/include/libpng16 -isystem /snap/flutter/current/usr/include/gdk-pixbuf-2.0 -isystem /snap/flutter/current/usr/include/libmount -isystem /snap/flutter/current/usr/include/blkid -isystem /snap/flutter/current/usr/include/glib-2.0 -isystem /snap/flutter/current/usr/lib/x86_64-linux-gnu/glib-2.0/include
  OBJECT_DIR = plugins/file_selector_linux/CMakeFiles/file_selector_linux_plugin.dir
  OBJECT_FILE_DIR = plugins/file_selector_linux/CMakeFiles/file_selector_linux_plugin.dir


# =============================================================================
# Link build statements for SHARED_LIBRARY target file_selector_linux_plugin


#############################################
# Link the shared library plugins/file_selector_linux/libfile_selector_linux_plugin.so

build plugins/file_selector_linux/libfile_selector_linux_plugin.so: CXX_SHARED_LIBRARY_LINKER__file_selector_linux_plugin plugins/file_selector_linux/CMakeFiles/file_selector_linux_plugin.dir/file_selector_plugin.cc.o plugins/file_selector_linux/CMakeFiles/file_selector_linux_plugin.dir/messages.g.cc.o | /home/<USER>/HVAC/FlutterHVAC-MD3/hvac_flutter_md3/linux/flutter/ephemeral/libflutter_linux_gtk.so /snap/flutter/current/usr/lib/x86_64-linux-gnu/libgtk-3.so /snap/flutter/current/usr/lib/x86_64-linux-gnu/libgdk-3.so /snap/flutter/current/usr/lib/x86_64-linux-gnu/libpangocairo-1.0.so /snap/flutter/current/usr/lib/x86_64-linux-gnu/libpango-1.0.so /snap/flutter/current/usr/lib/x86_64-linux-gnu/libharfbuzz.so /snap/flutter/current/usr/lib/x86_64-linux-gnu/libatk-1.0.so /snap/flutter/current/usr/lib/x86_64-linux-gnu/libcairo-gobject.so /snap/flutter/current/usr/lib/x86_64-linux-gnu/libcairo.so /snap/flutter/current/usr/lib/x86_64-linux-gnu/libgdk_pixbuf-2.0.so /snap/flutter/current/usr/lib/x86_64-linux-gnu/libgio-2.0.so /snap/flutter/current/usr/lib/x86_64-linux-gnu/libgobject-2.0.so /snap/flutter/current/usr/lib/x86_64-linux-gnu/libglib-2.0.so || flutter/flutter_assemble
  LANGUAGE_COMPILE_FLAGS = -g
  LINK_FLAGS = -B/snap/flutter/current/usr/lib/gcc/x86_64-linux-gnu/9 -B/snap/flutter/current/usr/lib/x86_64-linux-gnu -B/snap/flutter/current/lib/x86_64-linux-gnu -B/snap/flutter/current/usr/lib/ -L/snap/flutter/current/usr/lib/gcc/x86_64-linux-gnu/9 -L/snap/flutter/current/usr/lib/x86_64-linux-gnu -L/snap/flutter/current/lib/x86_64-linux-gnu -L/snap/flutter/current/usr/lib/ -lblkid -lgcrypt -llzma -llz4 -lgpg-error -luuid -lpthread -ldl -lepoxy -lfontconfig -B/snap/flutter/current/usr/lib/gcc/x86_64-linux-gnu/9 -B/snap/flutter/current/usr/lib/x86_64-linux-gnu -B/snap/flutter/current/lib/x86_64-linux-gnu -B/snap/flutter/current/usr/lib/ -L/snap/flutter/current/usr/lib/gcc/x86_64-linux-gnu/9 -L/snap/flutter/current/usr/lib/x86_64-linux-gnu -L/snap/flutter/current/lib/x86_64-linux-gnu -L/snap/flutter/current/usr/lib/ -lblkid -lgcrypt -llzma -llz4 -lgpg-error -luuid -lpthread -ldl -lepoxy -lfontconfig
  LINK_LIBRARIES = -Wl,-rpath,/home/<USER>/HVAC/FlutterHVAC-MD3/hvac_flutter_md3/linux/flutter/ephemeral  /home/<USER>/HVAC/FlutterHVAC-MD3/hvac_flutter_md3/linux/flutter/ephemeral/libflutter_linux_gtk.so  /snap/flutter/current/usr/lib/x86_64-linux-gnu/libgtk-3.so  /snap/flutter/current/usr/lib/x86_64-linux-gnu/libgdk-3.so  /snap/flutter/current/usr/lib/x86_64-linux-gnu/libpangocairo-1.0.so  /snap/flutter/current/usr/lib/x86_64-linux-gnu/libpango-1.0.so  /snap/flutter/current/usr/lib/x86_64-linux-gnu/libharfbuzz.so  /snap/flutter/current/usr/lib/x86_64-linux-gnu/libatk-1.0.so  /snap/flutter/current/usr/lib/x86_64-linux-gnu/libcairo-gobject.so  /snap/flutter/current/usr/lib/x86_64-linux-gnu/libcairo.so  /snap/flutter/current/usr/lib/x86_64-linux-gnu/libgdk_pixbuf-2.0.so  /snap/flutter/current/usr/lib/x86_64-linux-gnu/libgio-2.0.so  /snap/flutter/current/usr/lib/x86_64-linux-gnu/libgobject-2.0.so  /snap/flutter/current/usr/lib/x86_64-linux-gnu/libglib-2.0.so
  OBJECT_DIR = plugins/file_selector_linux/CMakeFiles/file_selector_linux_plugin.dir
  POST_BUILD = :
  PRE_LINK = :
  SONAME = libfile_selector_linux_plugin.so
  SONAME_FLAG = -Wl,-soname,
  TARGET_FILE = plugins/file_selector_linux/libfile_selector_linux_plugin.so
  TARGET_PDB = file_selector_linux_plugin.so.dbg

# =============================================================================
# Write statements declared in CMakeLists.txt:
# /home/<USER>/HVAC/FlutterHVAC-MD3/hvac_flutter_md3/linux/flutter/generated_plugins.cmake
# =============================================================================


#############################################
# Utility command for install/strip

build plugins/flutter_secure_storage_linux/CMakeFiles/install/strip.util: CUSTOM_COMMAND plugins/flutter_secure_storage_linux/all
  COMMAND = cd /home/<USER>/HVAC/FlutterHVAC-MD3/hvac_flutter_md3/build/linux/x64/debug/plugins/flutter_secure_storage_linux && /snap/flutter/149/usr/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
  DESC = Installing the project stripped...
  pool = console
  restat = 1

build plugins/flutter_secure_storage_linux/install/strip: phony plugins/flutter_secure_storage_linux/CMakeFiles/install/strip.util


#############################################
# Utility command for install/local

build plugins/flutter_secure_storage_linux/CMakeFiles/install/local.util: CUSTOM_COMMAND plugins/flutter_secure_storage_linux/all
  COMMAND = cd /home/<USER>/HVAC/FlutterHVAC-MD3/hvac_flutter_md3/build/linux/x64/debug/plugins/flutter_secure_storage_linux && /snap/flutter/149/usr/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
  DESC = Installing only the local directory...
  pool = console
  restat = 1

build plugins/flutter_secure_storage_linux/install/local: phony plugins/flutter_secure_storage_linux/CMakeFiles/install/local.util


#############################################
# Utility command for install

build plugins/flutter_secure_storage_linux/CMakeFiles/install.util: CUSTOM_COMMAND plugins/flutter_secure_storage_linux/all
  COMMAND = cd /home/<USER>/HVAC/FlutterHVAC-MD3/hvac_flutter_md3/build/linux/x64/debug/plugins/flutter_secure_storage_linux && /snap/flutter/149/usr/bin/cmake -P cmake_install.cmake
  DESC = Install the project...
  pool = console
  restat = 1

build plugins/flutter_secure_storage_linux/install: phony plugins/flutter_secure_storage_linux/CMakeFiles/install.util


#############################################
# Utility command for list_install_components

build plugins/flutter_secure_storage_linux/list_install_components: phony


#############################################
# Utility command for rebuild_cache

build plugins/flutter_secure_storage_linux/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cd /home/<USER>/HVAC/FlutterHVAC-MD3/hvac_flutter_md3/build/linux/x64/debug/plugins/flutter_secure_storage_linux && /snap/flutter/149/usr/bin/cmake -S/home/<USER>/HVAC/FlutterHVAC-MD3/hvac_flutter_md3/linux -B/home/<USER>/HVAC/FlutterHVAC-MD3/hvac_flutter_md3/build/linux/x64/debug
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build plugins/flutter_secure_storage_linux/rebuild_cache: phony plugins/flutter_secure_storage_linux/CMakeFiles/rebuild_cache.util


#############################################
# Utility command for edit_cache

build plugins/flutter_secure_storage_linux/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cd /home/<USER>/HVAC/FlutterHVAC-MD3/hvac_flutter_md3/build/linux/x64/debug/plugins/flutter_secure_storage_linux && /snap/flutter/149/usr/bin/cmake -E echo No\ interactive\ CMake\ dialog\ available.
  DESC = No interactive CMake dialog available...
  restat = 1

build plugins/flutter_secure_storage_linux/edit_cache: phony plugins/flutter_secure_storage_linux/CMakeFiles/edit_cache.util

# =============================================================================
# Object build statements for SHARED_LIBRARY target flutter_secure_storage_linux_plugin


#############################################
# Order-only phony target for flutter_secure_storage_linux_plugin

build cmake_object_order_depends_target_flutter_secure_storage_linux_plugin: phony || flutter/flutter_assemble

build plugins/flutter_secure_storage_linux/CMakeFiles/flutter_secure_storage_linux_plugin.dir/flutter_secure_storage_linux_plugin.cc.o: CXX_COMPILER__flutter_secure_storage_linux_plugin /home/<USER>/HVAC/FlutterHVAC-MD3/hvac_flutter_md3/linux/flutter/ephemeral/.plugin_symlinks/flutter_secure_storage_linux/linux/flutter_secure_storage_linux_plugin.cc || cmake_object_order_depends_target_flutter_secure_storage_linux_plugin
  DEFINES = -DAPPLICATION_ID=\"com.example.hvac_flutter_md3\" -DFLUTTER_PLUGIN_IMPL -Dflutter_secure_storage_linux_plugin_EXPORTS
  DEP_FILE = plugins/flutter_secure_storage_linux/CMakeFiles/flutter_secure_storage_linux_plugin.dir/flutter_secure_storage_linux_plugin.cc.o.d
  FLAGS = -g -fPIC -fvisibility=hidden   -Wall -Werror -pthread
  INCLUDES = -I/home/<USER>/HVAC/FlutterHVAC-MD3/hvac_flutter_md3/linux/flutter/ephemeral -isystem /snap/flutter/current/usr/include/libsecret-1 -isystem /snap/flutter/current/usr/include/libmount -isystem /snap/flutter/current/usr/include/blkid -isystem /snap/flutter/current/usr/include/glib-2.0 -isystem /snap/flutter/current/usr/lib/x86_64-linux-gnu/glib-2.0/include -isystem /snap/flutter/current/usr/include/gtk-3.0 -isystem /snap/flutter/current/usr/include/at-spi2-atk/2.0 -isystem /snap/flutter/current/usr/include/at-spi-2.0 -isystem /snap/flutter/current/usr/include/dbus-1.0 -isystem /snap/flutter/current/usr/lib/x86_64-linux-gnu/dbus-1.0/include -isystem /snap/flutter/current/usr/include/gio-unix-2.0 -isystem /snap/flutter/current/usr/include/cairo -isystem /snap/flutter/current/usr/include/pango-1.0 -isystem /snap/flutter/current/usr/include/fribidi -isystem /snap/flutter/current/usr/include/harfbuzz -isystem /snap/flutter/current/usr/include/atk-1.0 -isystem /snap/flutter/current/usr/include/pixman-1 -isystem /snap/flutter/current/usr/include/uuid -isystem /snap/flutter/current/usr/include/freetype2 -isystem /snap/flutter/current/usr/include/libpng16 -isystem /snap/flutter/current/usr/include/gdk-pixbuf-2.0
  OBJECT_DIR = plugins/flutter_secure_storage_linux/CMakeFiles/flutter_secure_storage_linux_plugin.dir
  OBJECT_FILE_DIR = plugins/flutter_secure_storage_linux/CMakeFiles/flutter_secure_storage_linux_plugin.dir


# =============================================================================
# Link build statements for SHARED_LIBRARY target flutter_secure_storage_linux_plugin


#############################################
# Link the shared library plugins/flutter_secure_storage_linux/libflutter_secure_storage_linux_plugin.so

build plugins/flutter_secure_storage_linux/libflutter_secure_storage_linux_plugin.so: CXX_SHARED_LIBRARY_LINKER__flutter_secure_storage_linux_plugin plugins/flutter_secure_storage_linux/CMakeFiles/flutter_secure_storage_linux_plugin.dir/flutter_secure_storage_linux_plugin.cc.o | /home/<USER>/HVAC/FlutterHVAC-MD3/hvac_flutter_md3/linux/flutter/ephemeral/libflutter_linux_gtk.so /snap/flutter/current/usr/lib/x86_64-linux-gnu/libgtk-3.so /snap/flutter/current/usr/lib/x86_64-linux-gnu/libgdk-3.so /snap/flutter/current/usr/lib/x86_64-linux-gnu/libpangocairo-1.0.so /snap/flutter/current/usr/lib/x86_64-linux-gnu/libpango-1.0.so /snap/flutter/current/usr/lib/x86_64-linux-gnu/libharfbuzz.so /snap/flutter/current/usr/lib/x86_64-linux-gnu/libatk-1.0.so /snap/flutter/current/usr/lib/x86_64-linux-gnu/libcairo-gobject.so /snap/flutter/current/usr/lib/x86_64-linux-gnu/libcairo.so /snap/flutter/current/usr/lib/x86_64-linux-gnu/libgdk_pixbuf-2.0.so /snap/flutter/current/usr/lib/x86_64-linux-gnu/libsecret-1.so /snap/flutter/current/usr/lib/x86_64-linux-gnu/libgio-2.0.so /snap/flutter/current/usr/lib/x86_64-linux-gnu/libgobject-2.0.so /snap/flutter/current/usr/lib/x86_64-linux-gnu/libglib-2.0.so || flutter/flutter_assemble
  LANGUAGE_COMPILE_FLAGS = -g
  LINK_FLAGS = -B/snap/flutter/current/usr/lib/gcc/x86_64-linux-gnu/9 -B/snap/flutter/current/usr/lib/x86_64-linux-gnu -B/snap/flutter/current/lib/x86_64-linux-gnu -B/snap/flutter/current/usr/lib/ -L/snap/flutter/current/usr/lib/gcc/x86_64-linux-gnu/9 -L/snap/flutter/current/usr/lib/x86_64-linux-gnu -L/snap/flutter/current/lib/x86_64-linux-gnu -L/snap/flutter/current/usr/lib/ -lblkid -lgcrypt -llzma -llz4 -lgpg-error -luuid -lpthread -ldl -lepoxy -lfontconfig -B/snap/flutter/current/usr/lib/gcc/x86_64-linux-gnu/9 -B/snap/flutter/current/usr/lib/x86_64-linux-gnu -B/snap/flutter/current/lib/x86_64-linux-gnu -B/snap/flutter/current/usr/lib/ -L/snap/flutter/current/usr/lib/gcc/x86_64-linux-gnu/9 -L/snap/flutter/current/usr/lib/x86_64-linux-gnu -L/snap/flutter/current/lib/x86_64-linux-gnu -L/snap/flutter/current/usr/lib/ -lblkid -lgcrypt -llzma -llz4 -lgpg-error -luuid -lpthread -ldl -lepoxy -lfontconfig
  LINK_LIBRARIES = -Wl,-rpath,/home/<USER>/HVAC/FlutterHVAC-MD3/hvac_flutter_md3/linux/flutter/ephemeral  /home/<USER>/HVAC/FlutterHVAC-MD3/hvac_flutter_md3/linux/flutter/ephemeral/libflutter_linux_gtk.so  /snap/flutter/current/usr/lib/x86_64-linux-gnu/libgtk-3.so  /snap/flutter/current/usr/lib/x86_64-linux-gnu/libgdk-3.so  /snap/flutter/current/usr/lib/x86_64-linux-gnu/libpangocairo-1.0.so  /snap/flutter/current/usr/lib/x86_64-linux-gnu/libpango-1.0.so  /snap/flutter/current/usr/lib/x86_64-linux-gnu/libharfbuzz.so  /snap/flutter/current/usr/lib/x86_64-linux-gnu/libatk-1.0.so  /snap/flutter/current/usr/lib/x86_64-linux-gnu/libcairo-gobject.so  /snap/flutter/current/usr/lib/x86_64-linux-gnu/libcairo.so  /snap/flutter/current/usr/lib/x86_64-linux-gnu/libgdk_pixbuf-2.0.so  /snap/flutter/current/usr/lib/x86_64-linux-gnu/libsecret-1.so  /snap/flutter/current/usr/lib/x86_64-linux-gnu/libgio-2.0.so  /snap/flutter/current/usr/lib/x86_64-linux-gnu/libgobject-2.0.so  /snap/flutter/current/usr/lib/x86_64-linux-gnu/libglib-2.0.so
  OBJECT_DIR = plugins/flutter_secure_storage_linux/CMakeFiles/flutter_secure_storage_linux_plugin.dir
  POST_BUILD = :
  PRE_LINK = :
  SONAME = libflutter_secure_storage_linux_plugin.so
  SONAME_FLAG = -Wl,-soname,
  TARGET_FILE = plugins/flutter_secure_storage_linux/libflutter_secure_storage_linux_plugin.so
  TARGET_PDB = flutter_secure_storage_linux_plugin.so.dbg

# =============================================================================
# Write statements declared in CMakeLists.txt:
# /home/<USER>/HVAC/FlutterHVAC-MD3/hvac_flutter_md3/linux/flutter/generated_plugins.cmake
# =============================================================================


#############################################
# Utility command for install/strip

build plugins/url_launcher_linux/CMakeFiles/install/strip.util: CUSTOM_COMMAND plugins/url_launcher_linux/all
  COMMAND = cd /home/<USER>/HVAC/FlutterHVAC-MD3/hvac_flutter_md3/build/linux/x64/debug/plugins/url_launcher_linux && /snap/flutter/149/usr/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
  DESC = Installing the project stripped...
  pool = console
  restat = 1

build plugins/url_launcher_linux/install/strip: phony plugins/url_launcher_linux/CMakeFiles/install/strip.util


#############################################
# Utility command for install/local

build plugins/url_launcher_linux/CMakeFiles/install/local.util: CUSTOM_COMMAND plugins/url_launcher_linux/all
  COMMAND = cd /home/<USER>/HVAC/FlutterHVAC-MD3/hvac_flutter_md3/build/linux/x64/debug/plugins/url_launcher_linux && /snap/flutter/149/usr/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
  DESC = Installing only the local directory...
  pool = console
  restat = 1

build plugins/url_launcher_linux/install/local: phony plugins/url_launcher_linux/CMakeFiles/install/local.util


#############################################
# Utility command for install

build plugins/url_launcher_linux/CMakeFiles/install.util: CUSTOM_COMMAND plugins/url_launcher_linux/all
  COMMAND = cd /home/<USER>/HVAC/FlutterHVAC-MD3/hvac_flutter_md3/build/linux/x64/debug/plugins/url_launcher_linux && /snap/flutter/149/usr/bin/cmake -P cmake_install.cmake
  DESC = Install the project...
  pool = console
  restat = 1

build plugins/url_launcher_linux/install: phony plugins/url_launcher_linux/CMakeFiles/install.util


#############################################
# Utility command for list_install_components

build plugins/url_launcher_linux/list_install_components: phony


#############################################
# Utility command for rebuild_cache

build plugins/url_launcher_linux/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cd /home/<USER>/HVAC/FlutterHVAC-MD3/hvac_flutter_md3/build/linux/x64/debug/plugins/url_launcher_linux && /snap/flutter/149/usr/bin/cmake -S/home/<USER>/HVAC/FlutterHVAC-MD3/hvac_flutter_md3/linux -B/home/<USER>/HVAC/FlutterHVAC-MD3/hvac_flutter_md3/build/linux/x64/debug
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build plugins/url_launcher_linux/rebuild_cache: phony plugins/url_launcher_linux/CMakeFiles/rebuild_cache.util


#############################################
# Utility command for edit_cache

build plugins/url_launcher_linux/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cd /home/<USER>/HVAC/FlutterHVAC-MD3/hvac_flutter_md3/build/linux/x64/debug/plugins/url_launcher_linux && /snap/flutter/149/usr/bin/cmake -E echo No\ interactive\ CMake\ dialog\ available.
  DESC = No interactive CMake dialog available...
  restat = 1

build plugins/url_launcher_linux/edit_cache: phony plugins/url_launcher_linux/CMakeFiles/edit_cache.util

# =============================================================================
# Object build statements for SHARED_LIBRARY target url_launcher_linux_plugin


#############################################
# Order-only phony target for url_launcher_linux_plugin

build cmake_object_order_depends_target_url_launcher_linux_plugin: phony || flutter/flutter_assemble

build plugins/url_launcher_linux/CMakeFiles/url_launcher_linux_plugin.dir/messages.g.cc.o: CXX_COMPILER__url_launcher_linux_plugin /home/<USER>/HVAC/FlutterHVAC-MD3/hvac_flutter_md3/linux/flutter/ephemeral/.plugin_symlinks/url_launcher_linux/linux/messages.g.cc || cmake_object_order_depends_target_url_launcher_linux_plugin
  DEFINES = -DFLUTTER_PLUGIN_IMPL -Durl_launcher_linux_plugin_EXPORTS
  DEP_FILE = plugins/url_launcher_linux/CMakeFiles/url_launcher_linux_plugin.dir/messages.g.cc.o.d
  FLAGS = -g -fPIC -fvisibility=hidden   -Wall -Werror -pthread
  INCLUDES = -I/home/<USER>/HVAC/FlutterHVAC-MD3/hvac_flutter_md3/linux/flutter/ephemeral -isystem /snap/flutter/current/usr/include/gtk-3.0 -isystem /snap/flutter/current/usr/include/at-spi2-atk/2.0 -isystem /snap/flutter/current/usr/include/at-spi-2.0 -isystem /snap/flutter/current/usr/include/dbus-1.0 -isystem /snap/flutter/current/usr/lib/x86_64-linux-gnu/dbus-1.0/include -isystem /snap/flutter/current/usr/include/gio-unix-2.0 -isystem /snap/flutter/current/usr/include/cairo -isystem /snap/flutter/current/usr/include/pango-1.0 -isystem /snap/flutter/current/usr/include/fribidi -isystem /snap/flutter/current/usr/include/harfbuzz -isystem /snap/flutter/current/usr/include/atk-1.0 -isystem /snap/flutter/current/usr/include/pixman-1 -isystem /snap/flutter/current/usr/include/uuid -isystem /snap/flutter/current/usr/include/freetype2 -isystem /snap/flutter/current/usr/include/libpng16 -isystem /snap/flutter/current/usr/include/gdk-pixbuf-2.0 -isystem /snap/flutter/current/usr/include/libmount -isystem /snap/flutter/current/usr/include/blkid -isystem /snap/flutter/current/usr/include/glib-2.0 -isystem /snap/flutter/current/usr/lib/x86_64-linux-gnu/glib-2.0/include
  OBJECT_DIR = plugins/url_launcher_linux/CMakeFiles/url_launcher_linux_plugin.dir
  OBJECT_FILE_DIR = plugins/url_launcher_linux/CMakeFiles/url_launcher_linux_plugin.dir

build plugins/url_launcher_linux/CMakeFiles/url_launcher_linux_plugin.dir/url_launcher_plugin.cc.o: CXX_COMPILER__url_launcher_linux_plugin /home/<USER>/HVAC/FlutterHVAC-MD3/hvac_flutter_md3/linux/flutter/ephemeral/.plugin_symlinks/url_launcher_linux/linux/url_launcher_plugin.cc || cmake_object_order_depends_target_url_launcher_linux_plugin
  DEFINES = -DFLUTTER_PLUGIN_IMPL -Durl_launcher_linux_plugin_EXPORTS
  DEP_FILE = plugins/url_launcher_linux/CMakeFiles/url_launcher_linux_plugin.dir/url_launcher_plugin.cc.o.d
  FLAGS = -g -fPIC -fvisibility=hidden   -Wall -Werror -pthread
  INCLUDES = -I/home/<USER>/HVAC/FlutterHVAC-MD3/hvac_flutter_md3/linux/flutter/ephemeral -isystem /snap/flutter/current/usr/include/gtk-3.0 -isystem /snap/flutter/current/usr/include/at-spi2-atk/2.0 -isystem /snap/flutter/current/usr/include/at-spi-2.0 -isystem /snap/flutter/current/usr/include/dbus-1.0 -isystem /snap/flutter/current/usr/lib/x86_64-linux-gnu/dbus-1.0/include -isystem /snap/flutter/current/usr/include/gio-unix-2.0 -isystem /snap/flutter/current/usr/include/cairo -isystem /snap/flutter/current/usr/include/pango-1.0 -isystem /snap/flutter/current/usr/include/fribidi -isystem /snap/flutter/current/usr/include/harfbuzz -isystem /snap/flutter/current/usr/include/atk-1.0 -isystem /snap/flutter/current/usr/include/pixman-1 -isystem /snap/flutter/current/usr/include/uuid -isystem /snap/flutter/current/usr/include/freetype2 -isystem /snap/flutter/current/usr/include/libpng16 -isystem /snap/flutter/current/usr/include/gdk-pixbuf-2.0 -isystem /snap/flutter/current/usr/include/libmount -isystem /snap/flutter/current/usr/include/blkid -isystem /snap/flutter/current/usr/include/glib-2.0 -isystem /snap/flutter/current/usr/lib/x86_64-linux-gnu/glib-2.0/include
  OBJECT_DIR = plugins/url_launcher_linux/CMakeFiles/url_launcher_linux_plugin.dir
  OBJECT_FILE_DIR = plugins/url_launcher_linux/CMakeFiles/url_launcher_linux_plugin.dir


# =============================================================================
# Link build statements for SHARED_LIBRARY target url_launcher_linux_plugin


#############################################
# Link the shared library plugins/url_launcher_linux/liburl_launcher_linux_plugin.so

build plugins/url_launcher_linux/liburl_launcher_linux_plugin.so: CXX_SHARED_LIBRARY_LINKER__url_launcher_linux_plugin plugins/url_launcher_linux/CMakeFiles/url_launcher_linux_plugin.dir/messages.g.cc.o plugins/url_launcher_linux/CMakeFiles/url_launcher_linux_plugin.dir/url_launcher_plugin.cc.o | /home/<USER>/HVAC/FlutterHVAC-MD3/hvac_flutter_md3/linux/flutter/ephemeral/libflutter_linux_gtk.so /snap/flutter/current/usr/lib/x86_64-linux-gnu/libgtk-3.so /snap/flutter/current/usr/lib/x86_64-linux-gnu/libgdk-3.so /snap/flutter/current/usr/lib/x86_64-linux-gnu/libpangocairo-1.0.so /snap/flutter/current/usr/lib/x86_64-linux-gnu/libpango-1.0.so /snap/flutter/current/usr/lib/x86_64-linux-gnu/libharfbuzz.so /snap/flutter/current/usr/lib/x86_64-linux-gnu/libatk-1.0.so /snap/flutter/current/usr/lib/x86_64-linux-gnu/libcairo-gobject.so /snap/flutter/current/usr/lib/x86_64-linux-gnu/libcairo.so /snap/flutter/current/usr/lib/x86_64-linux-gnu/libgdk_pixbuf-2.0.so /snap/flutter/current/usr/lib/x86_64-linux-gnu/libgio-2.0.so /snap/flutter/current/usr/lib/x86_64-linux-gnu/libgobject-2.0.so /snap/flutter/current/usr/lib/x86_64-linux-gnu/libglib-2.0.so || flutter/flutter_assemble
  LANGUAGE_COMPILE_FLAGS = -g
  LINK_FLAGS = -B/snap/flutter/current/usr/lib/gcc/x86_64-linux-gnu/9 -B/snap/flutter/current/usr/lib/x86_64-linux-gnu -B/snap/flutter/current/lib/x86_64-linux-gnu -B/snap/flutter/current/usr/lib/ -L/snap/flutter/current/usr/lib/gcc/x86_64-linux-gnu/9 -L/snap/flutter/current/usr/lib/x86_64-linux-gnu -L/snap/flutter/current/lib/x86_64-linux-gnu -L/snap/flutter/current/usr/lib/ -lblkid -lgcrypt -llzma -llz4 -lgpg-error -luuid -lpthread -ldl -lepoxy -lfontconfig -B/snap/flutter/current/usr/lib/gcc/x86_64-linux-gnu/9 -B/snap/flutter/current/usr/lib/x86_64-linux-gnu -B/snap/flutter/current/lib/x86_64-linux-gnu -B/snap/flutter/current/usr/lib/ -L/snap/flutter/current/usr/lib/gcc/x86_64-linux-gnu/9 -L/snap/flutter/current/usr/lib/x86_64-linux-gnu -L/snap/flutter/current/lib/x86_64-linux-gnu -L/snap/flutter/current/usr/lib/ -lblkid -lgcrypt -llzma -llz4 -lgpg-error -luuid -lpthread -ldl -lepoxy -lfontconfig
  LINK_LIBRARIES = -Wl,-rpath,/home/<USER>/HVAC/FlutterHVAC-MD3/hvac_flutter_md3/linux/flutter/ephemeral  /home/<USER>/HVAC/FlutterHVAC-MD3/hvac_flutter_md3/linux/flutter/ephemeral/libflutter_linux_gtk.so  /snap/flutter/current/usr/lib/x86_64-linux-gnu/libgtk-3.so  /snap/flutter/current/usr/lib/x86_64-linux-gnu/libgdk-3.so  /snap/flutter/current/usr/lib/x86_64-linux-gnu/libpangocairo-1.0.so  /snap/flutter/current/usr/lib/x86_64-linux-gnu/libpango-1.0.so  /snap/flutter/current/usr/lib/x86_64-linux-gnu/libharfbuzz.so  /snap/flutter/current/usr/lib/x86_64-linux-gnu/libatk-1.0.so  /snap/flutter/current/usr/lib/x86_64-linux-gnu/libcairo-gobject.so  /snap/flutter/current/usr/lib/x86_64-linux-gnu/libcairo.so  /snap/flutter/current/usr/lib/x86_64-linux-gnu/libgdk_pixbuf-2.0.so  /snap/flutter/current/usr/lib/x86_64-linux-gnu/libgio-2.0.so  /snap/flutter/current/usr/lib/x86_64-linux-gnu/libgobject-2.0.so  /snap/flutter/current/usr/lib/x86_64-linux-gnu/libglib-2.0.so
  OBJECT_DIR = plugins/url_launcher_linux/CMakeFiles/url_launcher_linux_plugin.dir
  POST_BUILD = :
  PRE_LINK = :
  SONAME = liburl_launcher_linux_plugin.so
  SONAME_FLAG = -Wl,-soname,
  TARGET_FILE = plugins/url_launcher_linux/liburl_launcher_linux_plugin.so
  TARGET_PDB = url_launcher_linux_plugin.so.dbg

# =============================================================================
# Target aliases.

build dynamic_color_plugin: phony plugins/dynamic_color/libdynamic_color_plugin.so

build file_selector_linux_plugin: phony plugins/file_selector_linux/libfile_selector_linux_plugin.so

build flutter_assemble: phony flutter/flutter_assemble

build flutter_secure_storage_linux_plugin: phony plugins/flutter_secure_storage_linux/libflutter_secure_storage_linux_plugin.so

build hvac_flutter_md3: phony intermediates_do_not_run/hvac_flutter_md3

build libdynamic_color_plugin.so: phony plugins/dynamic_color/libdynamic_color_plugin.so

build libfile_selector_linux_plugin.so: phony plugins/file_selector_linux/libfile_selector_linux_plugin.so

build libflutter_secure_storage_linux_plugin.so: phony plugins/flutter_secure_storage_linux/libflutter_secure_storage_linux_plugin.so

build liburl_launcher_linux_plugin.so: phony plugins/url_launcher_linux/liburl_launcher_linux_plugin.so

build url_launcher_linux_plugin: phony plugins/url_launcher_linux/liburl_launcher_linux_plugin.so

# =============================================================================
# Folder targets.

# =============================================================================

#############################################
# Folder: /home/<USER>/HVAC/FlutterHVAC-MD3/hvac_flutter_md3/build/linux/x64/debug

build all: phony flutter/all runner/all plugins/dynamic_color/all plugins/file_selector_linux/all plugins/flutter_secure_storage_linux/all plugins/url_launcher_linux/all

# =============================================================================

#############################################
# Folder: /home/<USER>/HVAC/FlutterHVAC-MD3/hvac_flutter_md3/build/linux/x64/debug/flutter

build flutter/all: phony

# =============================================================================

#############################################
# Folder: /home/<USER>/HVAC/FlutterHVAC-MD3/hvac_flutter_md3/build/linux/x64/debug/plugins/dynamic_color

build plugins/dynamic_color/all: phony plugins/dynamic_color/libdynamic_color_plugin.so

# =============================================================================

#############################################
# Folder: /home/<USER>/HVAC/FlutterHVAC-MD3/hvac_flutter_md3/build/linux/x64/debug/plugins/file_selector_linux

build plugins/file_selector_linux/all: phony plugins/file_selector_linux/libfile_selector_linux_plugin.so

# =============================================================================

#############################################
# Folder: /home/<USER>/HVAC/FlutterHVAC-MD3/hvac_flutter_md3/build/linux/x64/debug/plugins/flutter_secure_storage_linux

build plugins/flutter_secure_storage_linux/all: phony plugins/flutter_secure_storage_linux/libflutter_secure_storage_linux_plugin.so

# =============================================================================

#############################################
# Folder: /home/<USER>/HVAC/FlutterHVAC-MD3/hvac_flutter_md3/build/linux/x64/debug/plugins/url_launcher_linux

build plugins/url_launcher_linux/all: phony plugins/url_launcher_linux/liburl_launcher_linux_plugin.so

# =============================================================================

#############################################
# Folder: /home/<USER>/HVAC/FlutterHVAC-MD3/hvac_flutter_md3/build/linux/x64/debug/runner

build runner/all: phony intermediates_do_not_run/hvac_flutter_md3

# =============================================================================
# Built-in targets


#############################################
# Make the all target the default.

default all

#############################################
# Re-run CMake if any of its inputs changed.

build build.ninja: RERUN_CMAKE | /home/<USER>/HVAC/FlutterHVAC-MD3/hvac_flutter_md3/linux/CMakeLists.txt /home/<USER>/HVAC/FlutterHVAC-MD3/hvac_flutter_md3/linux/flutter/CMakeLists.txt /home/<USER>/HVAC/FlutterHVAC-MD3/hvac_flutter_md3/linux/flutter/ephemeral/.plugin_symlinks/dynamic_color/linux/CMakeLists.txt /home/<USER>/HVAC/FlutterHVAC-MD3/hvac_flutter_md3/linux/flutter/ephemeral/.plugin_symlinks/file_selector_linux/linux/CMakeLists.txt /home/<USER>/HVAC/FlutterHVAC-MD3/hvac_flutter_md3/linux/flutter/ephemeral/.plugin_symlinks/flutter_secure_storage_linux/linux/CMakeLists.txt /home/<USER>/HVAC/FlutterHVAC-MD3/hvac_flutter_md3/linux/flutter/ephemeral/.plugin_symlinks/url_launcher_linux/linux/CMakeLists.txt /home/<USER>/HVAC/FlutterHVAC-MD3/hvac_flutter_md3/linux/flutter/ephemeral/generated_config.cmake /home/<USER>/HVAC/FlutterHVAC-MD3/hvac_flutter_md3/linux/flutter/generated_plugins.cmake /home/<USER>/HVAC/FlutterHVAC-MD3/hvac_flutter_md3/linux/runner/CMakeLists.txt /snap/flutter/149/usr/share/cmake-3.16/Modules/CMakeCXXInformation.cmake /snap/flutter/149/usr/share/cmake-3.16/Modules/CMakeCheckCompilerFlagCommonPatterns.cmake /snap/flutter/149/usr/share/cmake-3.16/Modules/CMakeCommonLanguageInclude.cmake /snap/flutter/149/usr/share/cmake-3.16/Modules/CMakeGenericSystem.cmake /snap/flutter/149/usr/share/cmake-3.16/Modules/CMakeInitializeConfigs.cmake /snap/flutter/149/usr/share/cmake-3.16/Modules/CMakeLanguageInformation.cmake /snap/flutter/149/usr/share/cmake-3.16/Modules/CMakeSystemSpecificInformation.cmake /snap/flutter/149/usr/share/cmake-3.16/Modules/CMakeSystemSpecificInitialize.cmake /snap/flutter/149/usr/share/cmake-3.16/Modules/Compiler/CMakeCommonCompilerMacros.cmake /snap/flutter/149/usr/share/cmake-3.16/Modules/Compiler/Clang-CXX.cmake /snap/flutter/149/usr/share/cmake-3.16/Modules/Compiler/Clang.cmake /snap/flutter/149/usr/share/cmake-3.16/Modules/Compiler/GNU.cmake /snap/flutter/149/usr/share/cmake-3.16/Modules/FindPackageHandleStandardArgs.cmake /snap/flutter/149/usr/share/cmake-3.16/Modules/FindPackageMessage.cmake /snap/flutter/149/usr/share/cmake-3.16/Modules/FindPkgConfig.cmake /snap/flutter/149/usr/share/cmake-3.16/Modules/Internal/CMakeCheckCompilerFlag.cmake /snap/flutter/149/usr/share/cmake-3.16/Modules/Platform/Linux-Clang-CXX.cmake /snap/flutter/149/usr/share/cmake-3.16/Modules/Platform/Linux-GNU-CXX.cmake /snap/flutter/149/usr/share/cmake-3.16/Modules/Platform/Linux-GNU.cmake /snap/flutter/149/usr/share/cmake-3.16/Modules/Platform/Linux.cmake /snap/flutter/149/usr/share/cmake-3.16/Modules/Platform/UnixPaths.cmake CMakeCache.txt CMakeFiles/3.16.3/CMakeCXXCompiler.cmake CMakeFiles/3.16.3/CMakeSystem.cmake
  pool = console


#############################################
# A missing CMake input file is not an error.

build /home/<USER>/HVAC/FlutterHVAC-MD3/hvac_flutter_md3/linux/CMakeLists.txt /home/<USER>/HVAC/FlutterHVAC-MD3/hvac_flutter_md3/linux/flutter/CMakeLists.txt /home/<USER>/HVAC/FlutterHVAC-MD3/hvac_flutter_md3/linux/flutter/ephemeral/.plugin_symlinks/dynamic_color/linux/CMakeLists.txt /home/<USER>/HVAC/FlutterHVAC-MD3/hvac_flutter_md3/linux/flutter/ephemeral/.plugin_symlinks/file_selector_linux/linux/CMakeLists.txt /home/<USER>/HVAC/FlutterHVAC-MD3/hvac_flutter_md3/linux/flutter/ephemeral/.plugin_symlinks/flutter_secure_storage_linux/linux/CMakeLists.txt /home/<USER>/HVAC/FlutterHVAC-MD3/hvac_flutter_md3/linux/flutter/ephemeral/.plugin_symlinks/url_launcher_linux/linux/CMakeLists.txt /home/<USER>/HVAC/FlutterHVAC-MD3/hvac_flutter_md3/linux/flutter/ephemeral/generated_config.cmake /home/<USER>/HVAC/FlutterHVAC-MD3/hvac_flutter_md3/linux/flutter/generated_plugins.cmake /home/<USER>/HVAC/FlutterHVAC-MD3/hvac_flutter_md3/linux/runner/CMakeLists.txt /snap/flutter/149/usr/share/cmake-3.16/Modules/CMakeCXXInformation.cmake /snap/flutter/149/usr/share/cmake-3.16/Modules/CMakeCheckCompilerFlagCommonPatterns.cmake /snap/flutter/149/usr/share/cmake-3.16/Modules/CMakeCommonLanguageInclude.cmake /snap/flutter/149/usr/share/cmake-3.16/Modules/CMakeGenericSystem.cmake /snap/flutter/149/usr/share/cmake-3.16/Modules/CMakeInitializeConfigs.cmake /snap/flutter/149/usr/share/cmake-3.16/Modules/CMakeLanguageInformation.cmake /snap/flutter/149/usr/share/cmake-3.16/Modules/CMakeSystemSpecificInformation.cmake /snap/flutter/149/usr/share/cmake-3.16/Modules/CMakeSystemSpecificInitialize.cmake /snap/flutter/149/usr/share/cmake-3.16/Modules/Compiler/CMakeCommonCompilerMacros.cmake /snap/flutter/149/usr/share/cmake-3.16/Modules/Compiler/Clang-CXX.cmake /snap/flutter/149/usr/share/cmake-3.16/Modules/Compiler/Clang.cmake /snap/flutter/149/usr/share/cmake-3.16/Modules/Compiler/GNU.cmake /snap/flutter/149/usr/share/cmake-3.16/Modules/FindPackageHandleStandardArgs.cmake /snap/flutter/149/usr/share/cmake-3.16/Modules/FindPackageMessage.cmake /snap/flutter/149/usr/share/cmake-3.16/Modules/FindPkgConfig.cmake /snap/flutter/149/usr/share/cmake-3.16/Modules/Internal/CMakeCheckCompilerFlag.cmake /snap/flutter/149/usr/share/cmake-3.16/Modules/Platform/Linux-Clang-CXX.cmake /snap/flutter/149/usr/share/cmake-3.16/Modules/Platform/Linux-GNU-CXX.cmake /snap/flutter/149/usr/share/cmake-3.16/Modules/Platform/Linux-GNU.cmake /snap/flutter/149/usr/share/cmake-3.16/Modules/Platform/Linux.cmake /snap/flutter/149/usr/share/cmake-3.16/Modules/Platform/UnixPaths.cmake CMakeCache.txt CMakeFiles/3.16.3/CMakeCXXCompiler.cmake CMakeFiles/3.16.3/CMakeSystem.cmake: phony


#############################################
# Clean all the built files.

build clean: CLEAN


#############################################
# Print all primary targets available.

build help: HELP

