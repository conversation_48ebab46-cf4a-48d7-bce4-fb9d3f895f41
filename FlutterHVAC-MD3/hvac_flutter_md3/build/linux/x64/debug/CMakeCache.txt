# This is the CMakeCache file.
# For build in directory: /home/<USER>/HVAC/FlutterHVAC-MD3/hvac_flutter_md3/build/linux/x64/debug
# It was generated by CMake: /snap/flutter/149/usr/bin/cmake
# You can edit this file to change values found and used by cmake.
# If you do not want to change any of the values, simply exit the editor.
# If you do want to change a value, simply edit, save, and exit the editor.
# The syntax for the file is as follows:
# KEY:TYPE=VALUE
# KEY is the name of a variable in the cache.
# TYPE is a hint to GUIs for the type of VALUE, DO NOT EDIT TYPE!.
# VALUE is the current value for the KEY.

########################
# EXTERNAL cache entries
########################

//Path to a program.
CMAKE_ADDR2LINE:FILEPATH=/snap/flutter/current/usr/bin/addr2line

//Path to a program.
CMAKE_AR:FILEPATH=/snap/flutter/current/usr/bin/ar

//Choose the type of build, options are: None Debug Release RelWithDebInfo
// MinSizeRel ...
CMAKE_BUILD_TYPE:STRING=Debug

//CXX compiler
CMAKE_CXX_COMPILER:FILEPATH=/snap/flutter/current/usr/bin/clang++

//LLVM archiver
CMAKE_CXX_COMPILER_AR:FILEPATH=CMAKE_CXX_COMPILER_AR-NOTFOUND

//Generate index for LLVM archive
CMAKE_CXX_COMPILER_RANLIB:FILEPATH=CMAKE_CXX_COMPILER_RANLIB-NOTFOUND

//Flags used by the CXX compiler during all build types.
CMAKE_CXX_FLAGS:STRING=

//Flags used by the CXX compiler during DEBUG builds.
CMAKE_CXX_FLAGS_DEBUG:STRING=-g

//Flags used by the CXX compiler during MINSIZEREL builds.
CMAKE_CXX_FLAGS_MINSIZEREL:STRING=-Os -DNDEBUG

//Flags used by the CXX compiler during RELEASE builds.
CMAKE_CXX_FLAGS_RELEASE:STRING=-O3 -DNDEBUG

//Flags used by the CXX compiler during RELWITHDEBINFO builds.
CMAKE_CXX_FLAGS_RELWITHDEBINFO:STRING=-O2 -g -DNDEBUG

//Path to a program.
CMAKE_DLLTOOL:FILEPATH=CMAKE_DLLTOOL-NOTFOUND

//Flags used by the linker during all build types.
CMAKE_EXE_LINKER_FLAGS:STRING=-B/snap/flutter/current/usr/lib/gcc/x86_64-linux-gnu/9 -B/snap/flutter/current/usr/lib/x86_64-linux-gnu -B/snap/flutter/current/lib/x86_64-linux-gnu -B/snap/flutter/current/usr/lib/ -L/snap/flutter/current/usr/lib/gcc/x86_64-linux-gnu/9 -L/snap/flutter/current/usr/lib/x86_64-linux-gnu -L/snap/flutter/current/lib/x86_64-linux-gnu -L/snap/flutter/current/usr/lib/ -lblkid -lgcrypt -llzma -llz4 -lgpg-error -luuid -lpthread -ldl -lepoxy -lfontconfig -B/snap/flutter/current/usr/lib/gcc/x86_64-linux-gnu/9 -B/snap/flutter/current/usr/lib/x86_64-linux-gnu -B/snap/flutter/current/lib/x86_64-linux-gnu -B/snap/flutter/current/usr/lib/ -L/snap/flutter/current/usr/lib/gcc/x86_64-linux-gnu/9 -L/snap/flutter/current/usr/lib/x86_64-linux-gnu -L/snap/flutter/current/lib/x86_64-linux-gnu -L/snap/flutter/current/usr/lib/ -lblkid -lgcrypt -llzma -llz4 -lgpg-error -luuid -lpthread -ldl -lepoxy -lfontconfig

//Flags used by the linker during DEBUG builds.
CMAKE_EXE_LINKER_FLAGS_DEBUG:STRING=

//Flags used by the linker during MINSIZEREL builds.
CMAKE_EXE_LINKER_FLAGS_MINSIZEREL:STRING=

//Flags used by the linker during RELEASE builds.
CMAKE_EXE_LINKER_FLAGS_RELEASE:STRING=

//Flags used by the linker during RELWITHDEBINFO builds.
CMAKE_EXE_LINKER_FLAGS_RELWITHDEBINFO:STRING=

//Enable/Disable output of compile commands during generation.
CMAKE_EXPORT_COMPILE_COMMANDS:BOOL=OFF

//...
CMAKE_INSTALL_PREFIX:PATH=/home/<USER>/HVAC/FlutterHVAC-MD3/hvac_flutter_md3/build/linux/x64/debug/bundle

//Path to a program.
CMAKE_LINKER:FILEPATH=/snap/flutter/current/usr/bin/ld

//Program used to build from build.ninja files.
CMAKE_MAKE_PROGRAM:FILEPATH=/snap/flutter/current/usr/bin/ninja

//Flags used by the linker during the creation of modules during
// all build types.
CMAKE_MODULE_LINKER_FLAGS:STRING=-B/snap/flutter/current/usr/lib/gcc/x86_64-linux-gnu/9 -B/snap/flutter/current/usr/lib/x86_64-linux-gnu -B/snap/flutter/current/lib/x86_64-linux-gnu -B/snap/flutter/current/usr/lib/ -L/snap/flutter/current/usr/lib/gcc/x86_64-linux-gnu/9 -L/snap/flutter/current/usr/lib/x86_64-linux-gnu -L/snap/flutter/current/lib/x86_64-linux-gnu -L/snap/flutter/current/usr/lib/ -lblkid -lgcrypt -llzma -llz4 -lgpg-error -luuid -lpthread -ldl -lepoxy -lfontconfig -B/snap/flutter/current/usr/lib/gcc/x86_64-linux-gnu/9 -B/snap/flutter/current/usr/lib/x86_64-linux-gnu -B/snap/flutter/current/lib/x86_64-linux-gnu -B/snap/flutter/current/usr/lib/ -L/snap/flutter/current/usr/lib/gcc/x86_64-linux-gnu/9 -L/snap/flutter/current/usr/lib/x86_64-linux-gnu -L/snap/flutter/current/lib/x86_64-linux-gnu -L/snap/flutter/current/usr/lib/ -lblkid -lgcrypt -llzma -llz4 -lgpg-error -luuid -lpthread -ldl -lepoxy -lfontconfig

//Flags used by the linker during the creation of modules during
// DEBUG builds.
CMAKE_MODULE_LINKER_FLAGS_DEBUG:STRING=

//Flags used by the linker during the creation of modules during
// MINSIZEREL builds.
CMAKE_MODULE_LINKER_FLAGS_MINSIZEREL:STRING=

//Flags used by the linker during the creation of modules during
// RELEASE builds.
CMAKE_MODULE_LINKER_FLAGS_RELEASE:STRING=

//Flags used by the linker during the creation of modules during
// RELWITHDEBINFO builds.
CMAKE_MODULE_LINKER_FLAGS_RELWITHDEBINFO:STRING=

//Path to a program.
CMAKE_NM:FILEPATH=/snap/flutter/current/usr/bin/nm

//Path to a program.
CMAKE_OBJCOPY:FILEPATH=/snap/flutter/current/usr/bin/objcopy

//Path to a program.
CMAKE_OBJDUMP:FILEPATH=/snap/flutter/current/usr/bin/objdump

//Value Computed by CMake
CMAKE_PROJECT_DESCRIPTION:STATIC=

//Value Computed by CMake
CMAKE_PROJECT_HOMEPAGE_URL:STATIC=

//Value Computed by CMake
CMAKE_PROJECT_NAME:STATIC=runner

//Path to a program.
CMAKE_RANLIB:FILEPATH=/snap/flutter/current/usr/bin/ranlib

//Path to a program.
CMAKE_READELF:FILEPATH=/snap/flutter/current/usr/bin/readelf

//Flags used by the linker during the creation of shared libraries
// during all build types.
CMAKE_SHARED_LINKER_FLAGS:STRING=-B/snap/flutter/current/usr/lib/gcc/x86_64-linux-gnu/9 -B/snap/flutter/current/usr/lib/x86_64-linux-gnu -B/snap/flutter/current/lib/x86_64-linux-gnu -B/snap/flutter/current/usr/lib/ -L/snap/flutter/current/usr/lib/gcc/x86_64-linux-gnu/9 -L/snap/flutter/current/usr/lib/x86_64-linux-gnu -L/snap/flutter/current/lib/x86_64-linux-gnu -L/snap/flutter/current/usr/lib/ -lblkid -lgcrypt -llzma -llz4 -lgpg-error -luuid -lpthread -ldl -lepoxy -lfontconfig -B/snap/flutter/current/usr/lib/gcc/x86_64-linux-gnu/9 -B/snap/flutter/current/usr/lib/x86_64-linux-gnu -B/snap/flutter/current/lib/x86_64-linux-gnu -B/snap/flutter/current/usr/lib/ -L/snap/flutter/current/usr/lib/gcc/x86_64-linux-gnu/9 -L/snap/flutter/current/usr/lib/x86_64-linux-gnu -L/snap/flutter/current/lib/x86_64-linux-gnu -L/snap/flutter/current/usr/lib/ -lblkid -lgcrypt -llzma -llz4 -lgpg-error -luuid -lpthread -ldl -lepoxy -lfontconfig

//Flags used by the linker during the creation of shared libraries
// during DEBUG builds.
CMAKE_SHARED_LINKER_FLAGS_DEBUG:STRING=

//Flags used by the linker during the creation of shared libraries
// during MINSIZEREL builds.
CMAKE_SHARED_LINKER_FLAGS_MINSIZEREL:STRING=

//Flags used by the linker during the creation of shared libraries
// during RELEASE builds.
CMAKE_SHARED_LINKER_FLAGS_RELEASE:STRING=

//Flags used by the linker during the creation of shared libraries
// during RELWITHDEBINFO builds.
CMAKE_SHARED_LINKER_FLAGS_RELWITHDEBINFO:STRING=

//If set, runtime paths are not added when installing shared libraries,
// but are added when building.
CMAKE_SKIP_INSTALL_RPATH:BOOL=NO

//If set, runtime paths are not added when using shared libraries.
CMAKE_SKIP_RPATH:BOOL=NO

//Flags used by the linker during the creation of static libraries
// during all build types.
CMAKE_STATIC_LINKER_FLAGS:STRING=

//Flags used by the linker during the creation of static libraries
// during DEBUG builds.
CMAKE_STATIC_LINKER_FLAGS_DEBUG:STRING=

//Flags used by the linker during the creation of static libraries
// during MINSIZEREL builds.
CMAKE_STATIC_LINKER_FLAGS_MINSIZEREL:STRING=

//Flags used by the linker during the creation of static libraries
// during RELEASE builds.
CMAKE_STATIC_LINKER_FLAGS_RELEASE:STRING=

//Flags used by the linker during the creation of static libraries
// during RELWITHDEBINFO builds.
CMAKE_STATIC_LINKER_FLAGS_RELWITHDEBINFO:STRING=

//Path to a program.
CMAKE_STRIP:FILEPATH=/snap/flutter/current/usr/bin/strip

//If this value is on, makefiles will be generated without the
// .SILENT directive, and all commands will be echoed to the console
// during the make.  This is useful for debugging only. With Visual
// Studio IDE projects all commands are done without /nologo.
CMAKE_VERBOSE_MAKEFILE:BOOL=FALSE

//No help, variable specified on the command line.
FLUTTER_TARGET_PLATFORM:UNINITIALIZED=linux-x64

//pkg-config executable
PKG_CONFIG_EXECUTABLE:FILEPATH=/snap/flutter/current/usr/bin/pkg-config

//Value Computed by CMake
dynamic_color_BINARY_DIR:STATIC=/home/<USER>/HVAC/FlutterHVAC-MD3/hvac_flutter_md3/build/linux/x64/debug/plugins/dynamic_color

//Value Computed by CMake
dynamic_color_SOURCE_DIR:STATIC=/home/<USER>/HVAC/FlutterHVAC-MD3/hvac_flutter_md3/linux/flutter/ephemeral/.plugin_symlinks/dynamic_color/linux

//Value Computed by CMake
file_selector_linux_BINARY_DIR:STATIC=/home/<USER>/HVAC/FlutterHVAC-MD3/hvac_flutter_md3/build/linux/x64/debug/plugins/file_selector_linux

//Value Computed by CMake
file_selector_linux_SOURCE_DIR:STATIC=/home/<USER>/HVAC/FlutterHVAC-MD3/hvac_flutter_md3/linux/flutter/ephemeral/.plugin_symlinks/file_selector_linux/linux

//Value Computed by CMake
flutter_secure_storage_linux_BINARY_DIR:STATIC=/home/<USER>/HVAC/FlutterHVAC-MD3/hvac_flutter_md3/build/linux/x64/debug/plugins/flutter_secure_storage_linux

//Value Computed by CMake
flutter_secure_storage_linux_SOURCE_DIR:STATIC=/home/<USER>/HVAC/FlutterHVAC-MD3/hvac_flutter_md3/linux/flutter/ephemeral/.plugin_symlinks/flutter_secure_storage_linux/linux

//Path to a library.
pkgcfg_lib_GIO_gio-2.0:FILEPATH=/snap/flutter/current/usr/lib/x86_64-linux-gnu/libgio-2.0.so

//Path to a library.
pkgcfg_lib_GIO_glib-2.0:FILEPATH=/snap/flutter/current/usr/lib/x86_64-linux-gnu/libglib-2.0.so

//Path to a library.
pkgcfg_lib_GIO_gobject-2.0:FILEPATH=/snap/flutter/current/usr/lib/x86_64-linux-gnu/libgobject-2.0.so

//Path to a library.
pkgcfg_lib_GLIB_glib-2.0:FILEPATH=/snap/flutter/current/usr/lib/x86_64-linux-gnu/libglib-2.0.so

//Path to a library.
pkgcfg_lib_GTK_atk-1.0:FILEPATH=/snap/flutter/current/usr/lib/x86_64-linux-gnu/libatk-1.0.so

//Path to a library.
pkgcfg_lib_GTK_cairo:FILEPATH=/snap/flutter/current/usr/lib/x86_64-linux-gnu/libcairo.so

//Path to a library.
pkgcfg_lib_GTK_cairo-gobject:FILEPATH=/snap/flutter/current/usr/lib/x86_64-linux-gnu/libcairo-gobject.so

//Path to a library.
pkgcfg_lib_GTK_gdk-3:FILEPATH=/snap/flutter/current/usr/lib/x86_64-linux-gnu/libgdk-3.so

//Path to a library.
pkgcfg_lib_GTK_gdk_pixbuf-2.0:FILEPATH=/snap/flutter/current/usr/lib/x86_64-linux-gnu/libgdk_pixbuf-2.0.so

//Path to a library.
pkgcfg_lib_GTK_gio-2.0:FILEPATH=/snap/flutter/current/usr/lib/x86_64-linux-gnu/libgio-2.0.so

//Path to a library.
pkgcfg_lib_GTK_glib-2.0:FILEPATH=/snap/flutter/current/usr/lib/x86_64-linux-gnu/libglib-2.0.so

//Path to a library.
pkgcfg_lib_GTK_gobject-2.0:FILEPATH=/snap/flutter/current/usr/lib/x86_64-linux-gnu/libgobject-2.0.so

//Path to a library.
pkgcfg_lib_GTK_gtk-3:FILEPATH=/snap/flutter/current/usr/lib/x86_64-linux-gnu/libgtk-3.so

//Path to a library.
pkgcfg_lib_GTK_harfbuzz:FILEPATH=/snap/flutter/current/usr/lib/x86_64-linux-gnu/libharfbuzz.so

//Path to a library.
pkgcfg_lib_GTK_pango-1.0:FILEPATH=/snap/flutter/current/usr/lib/x86_64-linux-gnu/libpango-1.0.so

//Path to a library.
pkgcfg_lib_GTK_pangocairo-1.0:FILEPATH=/snap/flutter/current/usr/lib/x86_64-linux-gnu/libpangocairo-1.0.so

//Path to a library.
pkgcfg_lib_LIBSECRET_gio-2.0:FILEPATH=/snap/flutter/current/usr/lib/x86_64-linux-gnu/libgio-2.0.so

//Path to a library.
pkgcfg_lib_LIBSECRET_glib-2.0:FILEPATH=/snap/flutter/current/usr/lib/x86_64-linux-gnu/libglib-2.0.so

//Path to a library.
pkgcfg_lib_LIBSECRET_gobject-2.0:FILEPATH=/snap/flutter/current/usr/lib/x86_64-linux-gnu/libgobject-2.0.so

//Path to a library.
pkgcfg_lib_LIBSECRET_secret-1:FILEPATH=/snap/flutter/current/usr/lib/x86_64-linux-gnu/libsecret-1.so

//Value Computed by CMake
runner_BINARY_DIR:STATIC=/home/<USER>/HVAC/FlutterHVAC-MD3/hvac_flutter_md3/build/linux/x64/debug/runner

//Value Computed by CMake
runner_SOURCE_DIR:STATIC=/home/<USER>/HVAC/FlutterHVAC-MD3/hvac_flutter_md3/linux/runner

//Value Computed by CMake
url_launcher_linux_BINARY_DIR:STATIC=/home/<USER>/HVAC/FlutterHVAC-MD3/hvac_flutter_md3/build/linux/x64/debug/plugins/url_launcher_linux

//Value Computed by CMake
url_launcher_linux_SOURCE_DIR:STATIC=/home/<USER>/HVAC/FlutterHVAC-MD3/hvac_flutter_md3/linux/flutter/ephemeral/.plugin_symlinks/url_launcher_linux/linux


########################
# INTERNAL cache entries
########################

//ADVANCED property for variable: CMAKE_ADDR2LINE
CMAKE_ADDR2LINE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_AR
CMAKE_AR-ADVANCED:INTERNAL=1
//This is the directory where this CMakeCache.txt was created
CMAKE_CACHEFILE_DIR:INTERNAL=/home/<USER>/HVAC/FlutterHVAC-MD3/hvac_flutter_md3/build/linux/x64/debug
//Major version of cmake used to create the current loaded cache
CMAKE_CACHE_MAJOR_VERSION:INTERNAL=3
//Minor version of cmake used to create the current loaded cache
CMAKE_CACHE_MINOR_VERSION:INTERNAL=16
//Patch version of cmake used to create the current loaded cache
CMAKE_CACHE_PATCH_VERSION:INTERNAL=3
//Path to CMake executable.
CMAKE_COMMAND:INTERNAL=/snap/flutter/149/usr/bin/cmake
//Path to cpack program executable.
CMAKE_CPACK_COMMAND:INTERNAL=/snap/flutter/149/usr/bin/cpack
//Path to ctest program executable.
CMAKE_CTEST_COMMAND:INTERNAL=/snap/flutter/149/usr/bin/ctest
//ADVANCED property for variable: CMAKE_CXX_COMPILER
CMAKE_CXX_COMPILER-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_COMPILER_AR
CMAKE_CXX_COMPILER_AR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_COMPILER_RANLIB
CMAKE_CXX_COMPILER_RANLIB-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_FLAGS
CMAKE_CXX_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_FLAGS_DEBUG
CMAKE_CXX_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_FLAGS_MINSIZEREL
CMAKE_CXX_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_FLAGS_RELEASE
CMAKE_CXX_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_FLAGS_RELWITHDEBINFO
CMAKE_CXX_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_DLLTOOL
CMAKE_DLLTOOL-ADVANCED:INTERNAL=1
//Executable file format
CMAKE_EXECUTABLE_FORMAT:INTERNAL=ELF
//ADVANCED property for variable: CMAKE_EXE_LINKER_FLAGS
CMAKE_EXE_LINKER_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_EXE_LINKER_FLAGS_DEBUG
CMAKE_EXE_LINKER_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_EXE_LINKER_FLAGS_MINSIZEREL
CMAKE_EXE_LINKER_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_EXE_LINKER_FLAGS_RELEASE
CMAKE_EXE_LINKER_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_EXE_LINKER_FLAGS_RELWITHDEBINFO
CMAKE_EXE_LINKER_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_EXPORT_COMPILE_COMMANDS
CMAKE_EXPORT_COMPILE_COMMANDS-ADVANCED:INTERNAL=1
//Name of external makefile project generator.
CMAKE_EXTRA_GENERATOR:INTERNAL=
//Name of generator.
CMAKE_GENERATOR:INTERNAL=Ninja
//Generator instance identifier.
CMAKE_GENERATOR_INSTANCE:INTERNAL=
//Name of generator platform.
CMAKE_GENERATOR_PLATFORM:INTERNAL=
//Name of generator toolset.
CMAKE_GENERATOR_TOOLSET:INTERNAL=
//Source directory with the top level CMakeLists.txt file for this
// project
CMAKE_HOME_DIRECTORY:INTERNAL=/home/<USER>/HVAC/FlutterHVAC-MD3/hvac_flutter_md3/linux
//Install .so files without execute permission.
CMAKE_INSTALL_SO_NO_EXE:INTERNAL=1
//ADVANCED property for variable: CMAKE_LINKER
CMAKE_LINKER-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MAKE_PROGRAM
CMAKE_MAKE_PROGRAM-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MODULE_LINKER_FLAGS
CMAKE_MODULE_LINKER_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MODULE_LINKER_FLAGS_DEBUG
CMAKE_MODULE_LINKER_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MODULE_LINKER_FLAGS_MINSIZEREL
CMAKE_MODULE_LINKER_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MODULE_LINKER_FLAGS_RELEASE
CMAKE_MODULE_LINKER_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MODULE_LINKER_FLAGS_RELWITHDEBINFO
CMAKE_MODULE_LINKER_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_NM
CMAKE_NM-ADVANCED:INTERNAL=1
//number of local generators
CMAKE_NUMBER_OF_MAKEFILES:INTERNAL=7
//ADVANCED property for variable: CMAKE_OBJCOPY
CMAKE_OBJCOPY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_OBJDUMP
CMAKE_OBJDUMP-ADVANCED:INTERNAL=1
//Platform information initialized
CMAKE_PLATFORM_INFO_INITIALIZED:INTERNAL=1
//ADVANCED property for variable: CMAKE_RANLIB
CMAKE_RANLIB-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_READELF
CMAKE_READELF-ADVANCED:INTERNAL=1
//Path to CMake installation.
CMAKE_ROOT:INTERNAL=/snap/flutter/149/usr/share/cmake-3.16
//ADVANCED property for variable: CMAKE_SHARED_LINKER_FLAGS
CMAKE_SHARED_LINKER_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SHARED_LINKER_FLAGS_DEBUG
CMAKE_SHARED_LINKER_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SHARED_LINKER_FLAGS_MINSIZEREL
CMAKE_SHARED_LINKER_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SHARED_LINKER_FLAGS_RELEASE
CMAKE_SHARED_LINKER_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SHARED_LINKER_FLAGS_RELWITHDEBINFO
CMAKE_SHARED_LINKER_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SKIP_INSTALL_RPATH
CMAKE_SKIP_INSTALL_RPATH-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SKIP_RPATH
CMAKE_SKIP_RPATH-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STATIC_LINKER_FLAGS
CMAKE_STATIC_LINKER_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STATIC_LINKER_FLAGS_DEBUG
CMAKE_STATIC_LINKER_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STATIC_LINKER_FLAGS_MINSIZEREL
CMAKE_STATIC_LINKER_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STATIC_LINKER_FLAGS_RELEASE
CMAKE_STATIC_LINKER_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STATIC_LINKER_FLAGS_RELWITHDEBINFO
CMAKE_STATIC_LINKER_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STRIP
CMAKE_STRIP-ADVANCED:INTERNAL=1
//uname command
CMAKE_UNAME:INTERNAL=/usr/bin/uname
//ADVANCED property for variable: CMAKE_VERBOSE_MAKEFILE
CMAKE_VERBOSE_MAKEFILE-ADVANCED:INTERNAL=1
//Details about finding PkgConfig
FIND_PACKAGE_MESSAGE_DETAILS_PkgConfig:INTERNAL=[/snap/flutter/current/usr/bin/pkg-config][v0.29.1()]
GIO_CFLAGS:INTERNAL=-pthread;-I/snap/flutter/current/usr/include/libmount;-I/snap/flutter/current/usr/include/blkid;-I/snap/flutter/current/usr/include/glib-2.0;-I/snap/flutter/current/usr/lib/x86_64-linux-gnu/glib-2.0/include
GIO_CFLAGS_I:INTERNAL=
GIO_CFLAGS_OTHER:INTERNAL=-pthread
GIO_FOUND:INTERNAL=1
GIO_INCLUDEDIR:INTERNAL=/snap/flutter/current/usr/include
GIO_INCLUDE_DIRS:INTERNAL=/snap/flutter/current/usr/include/libmount;/snap/flutter/current/usr/include/blkid;/snap/flutter/current/usr/include/glib-2.0;/snap/flutter/current/usr/lib/x86_64-linux-gnu/glib-2.0/include
GIO_LDFLAGS:INTERNAL=-L/snap/flutter/current/usr/lib/x86_64-linux-gnu;-lgio-2.0;-lgobject-2.0;-lglib-2.0
GIO_LDFLAGS_OTHER:INTERNAL=
GIO_LIBDIR:INTERNAL=/snap/flutter/current/usr/lib/x86_64-linux-gnu
GIO_LIBRARIES:INTERNAL=gio-2.0;gobject-2.0;glib-2.0
GIO_LIBRARY_DIRS:INTERNAL=/snap/flutter/current/usr/lib/x86_64-linux-gnu
GIO_LIBS:INTERNAL=
GIO_LIBS_L:INTERNAL=
GIO_LIBS_OTHER:INTERNAL=
GIO_LIBS_PATHS:INTERNAL=
GIO_MODULE_NAME:INTERNAL=gio-2.0
GIO_PREFIX:INTERNAL=/snap/flutter/current/usr
GIO_STATIC_CFLAGS:INTERNAL=-pthread;-I/snap/flutter/current/usr/include/libmount;-I/snap/flutter/current/usr/include/blkid;-I/snap/flutter/current/usr/include/glib-2.0;-I/snap/flutter/current/usr/lib/x86_64-linux-gnu/glib-2.0/include
GIO_STATIC_CFLAGS_I:INTERNAL=
GIO_STATIC_CFLAGS_OTHER:INTERNAL=-pthread
GIO_STATIC_INCLUDE_DIRS:INTERNAL=/snap/flutter/current/usr/include/libmount;/snap/flutter/current/usr/include/blkid;/snap/flutter/current/usr/include/glib-2.0;/snap/flutter/current/usr/lib/x86_64-linux-gnu/glib-2.0/include
GIO_STATIC_LDFLAGS:INTERNAL=-L/snap/flutter/current/usr/lib/x86_64-linux-gnu;-L/snap/flutter/current/usr/lib;-L/snap/flutter/current/usr/lib/x86_64-linux-gnu;-lgio-2.0;-ldl;-pthread;-lresolv;-lgmodule-2.0;-pthread;-ldl;-lz;-lmount;-lblkid;-lselinux;-lsepol;-lpcre2-8;-pthread;-lgobject-2.0;-pthread;-lffi;-lglib-2.0;-pthread;-lpcre;-pthread
GIO_STATIC_LDFLAGS_OTHER:INTERNAL=-pthread
GIO_STATIC_LIBDIR:INTERNAL=
GIO_STATIC_LIBRARIES:INTERNAL=gio-2.0;dl;resolv;gmodule-2.0;dl;z;mount;blkid;selinux;sepol;pcre2-8;gobject-2.0;ffi;glib-2.0;pcre
GIO_STATIC_LIBRARY_DIRS:INTERNAL=/snap/flutter/current/usr/lib/x86_64-linux-gnu;/snap/flutter/current/usr/lib;/snap/flutter/current/usr/lib/x86_64-linux-gnu
GIO_STATIC_LIBS:INTERNAL=
GIO_STATIC_LIBS_L:INTERNAL=
GIO_STATIC_LIBS_OTHER:INTERNAL=
GIO_STATIC_LIBS_PATHS:INTERNAL=
GIO_VERSION:INTERNAL=2.64.6
GIO_gio-2.0_INCLUDEDIR:INTERNAL=
GIO_gio-2.0_LIBDIR:INTERNAL=
GIO_gio-2.0_PREFIX:INTERNAL=
GIO_gio-2.0_VERSION:INTERNAL=
GLIB_CFLAGS:INTERNAL=-I/snap/flutter/current/usr/include/glib-2.0;-I/snap/flutter/current/usr/lib/x86_64-linux-gnu/glib-2.0/include
GLIB_CFLAGS_I:INTERNAL=
GLIB_CFLAGS_OTHER:INTERNAL=
GLIB_FOUND:INTERNAL=1
GLIB_INCLUDEDIR:INTERNAL=/snap/flutter/current/usr/include
GLIB_INCLUDE_DIRS:INTERNAL=/snap/flutter/current/usr/include/glib-2.0;/snap/flutter/current/usr/lib/x86_64-linux-gnu/glib-2.0/include
GLIB_LDFLAGS:INTERNAL=-L/snap/flutter/current/usr/lib/x86_64-linux-gnu;-lglib-2.0
GLIB_LDFLAGS_OTHER:INTERNAL=
GLIB_LIBDIR:INTERNAL=/snap/flutter/current/usr/lib/x86_64-linux-gnu
GLIB_LIBRARIES:INTERNAL=glib-2.0
GLIB_LIBRARY_DIRS:INTERNAL=/snap/flutter/current/usr/lib/x86_64-linux-gnu
GLIB_LIBS:INTERNAL=
GLIB_LIBS_L:INTERNAL=
GLIB_LIBS_OTHER:INTERNAL=
GLIB_LIBS_PATHS:INTERNAL=
GLIB_MODULE_NAME:INTERNAL=glib-2.0
GLIB_PREFIX:INTERNAL=/snap/flutter/current/usr
GLIB_STATIC_CFLAGS:INTERNAL=-I/snap/flutter/current/usr/include/glib-2.0;-I/snap/flutter/current/usr/lib/x86_64-linux-gnu/glib-2.0/include
GLIB_STATIC_CFLAGS_I:INTERNAL=
GLIB_STATIC_CFLAGS_OTHER:INTERNAL=
GLIB_STATIC_INCLUDE_DIRS:INTERNAL=/snap/flutter/current/usr/include/glib-2.0;/snap/flutter/current/usr/lib/x86_64-linux-gnu/glib-2.0/include
GLIB_STATIC_LDFLAGS:INTERNAL=-L/snap/flutter/current/usr/lib/x86_64-linux-gnu;-lglib-2.0;-pthread;-lpcre;-pthread
GLIB_STATIC_LDFLAGS_OTHER:INTERNAL=-pthread
GLIB_STATIC_LIBDIR:INTERNAL=
GLIB_STATIC_LIBRARIES:INTERNAL=glib-2.0;pcre
GLIB_STATIC_LIBRARY_DIRS:INTERNAL=/snap/flutter/current/usr/lib/x86_64-linux-gnu
GLIB_STATIC_LIBS:INTERNAL=
GLIB_STATIC_LIBS_L:INTERNAL=
GLIB_STATIC_LIBS_OTHER:INTERNAL=
GLIB_STATIC_LIBS_PATHS:INTERNAL=
GLIB_VERSION:INTERNAL=2.64.6
GLIB_glib-2.0_INCLUDEDIR:INTERNAL=
GLIB_glib-2.0_LIBDIR:INTERNAL=
GLIB_glib-2.0_PREFIX:INTERNAL=
GLIB_glib-2.0_VERSION:INTERNAL=
GTK_CFLAGS:INTERNAL=-pthread;-I/snap/flutter/current/usr/include/gtk-3.0;-I/snap/flutter/current/usr/include/at-spi2-atk/2.0;-I/snap/flutter/current/usr/include/at-spi-2.0;-I/snap/flutter/current/usr/include/dbus-1.0;-I/snap/flutter/current/usr/lib/x86_64-linux-gnu/dbus-1.0/include;-I/snap/flutter/current/usr/include/gtk-3.0;-I/snap/flutter/current/usr/include/gio-unix-2.0;-I/snap/flutter/current/usr/include/cairo;-I/snap/flutter/current/usr/include/pango-1.0;-I/snap/flutter/current/usr/include/fribidi;-I/snap/flutter/current/usr/include/harfbuzz;-I/snap/flutter/current/usr/include/atk-1.0;-I/snap/flutter/current/usr/include/cairo;-I/snap/flutter/current/usr/include/pixman-1;-I/snap/flutter/current/usr/include/uuid;-I/snap/flutter/current/usr/include/freetype2;-I/snap/flutter/current/usr/include/libpng16;-I/snap/flutter/current/usr/include/gdk-pixbuf-2.0;-I/snap/flutter/current/usr/include/libmount;-I/snap/flutter/current/usr/include/blkid;-I/snap/flutter/current/usr/include/glib-2.0;-I/snap/flutter/current/usr/lib/x86_64-linux-gnu/glib-2.0/include
GTK_CFLAGS_I:INTERNAL=
GTK_CFLAGS_OTHER:INTERNAL=-pthread
GTK_FOUND:INTERNAL=1
GTK_INCLUDEDIR:INTERNAL=/snap/flutter/current/usr/include
GTK_INCLUDE_DIRS:INTERNAL=/snap/flutter/current/usr/include/gtk-3.0;/snap/flutter/current/usr/include/at-spi2-atk/2.0;/snap/flutter/current/usr/include/at-spi-2.0;/snap/flutter/current/usr/include/dbus-1.0;/snap/flutter/current/usr/lib/x86_64-linux-gnu/dbus-1.0/include;/snap/flutter/current/usr/include/gtk-3.0;/snap/flutter/current/usr/include/gio-unix-2.0;/snap/flutter/current/usr/include/cairo;/snap/flutter/current/usr/include/pango-1.0;/snap/flutter/current/usr/include/fribidi;/snap/flutter/current/usr/include/harfbuzz;/snap/flutter/current/usr/include/atk-1.0;/snap/flutter/current/usr/include/cairo;/snap/flutter/current/usr/include/pixman-1;/snap/flutter/current/usr/include/uuid;/snap/flutter/current/usr/include/freetype2;/snap/flutter/current/usr/include/libpng16;/snap/flutter/current/usr/include/gdk-pixbuf-2.0;/snap/flutter/current/usr/include/libmount;/snap/flutter/current/usr/include/blkid;/snap/flutter/current/usr/include/glib-2.0;/snap/flutter/current/usr/lib/x86_64-linux-gnu/glib-2.0/include
GTK_LDFLAGS:INTERNAL=-L/snap/flutter/current/usr/lib/x86_64-linux-gnu;-lgtk-3;-lgdk-3;-lpangocairo-1.0;-lpango-1.0;-lharfbuzz;-latk-1.0;-lcairo-gobject;-lcairo;-lgdk_pixbuf-2.0;-lgio-2.0;-lgobject-2.0;-lglib-2.0
GTK_LDFLAGS_OTHER:INTERNAL=
GTK_LIBDIR:INTERNAL=/snap/flutter/current/usr/lib/x86_64-linux-gnu
GTK_LIBRARIES:INTERNAL=gtk-3;gdk-3;pangocairo-1.0;pango-1.0;harfbuzz;atk-1.0;cairo-gobject;cairo;gdk_pixbuf-2.0;gio-2.0;gobject-2.0;glib-2.0
GTK_LIBRARY_DIRS:INTERNAL=/snap/flutter/current/usr/lib/x86_64-linux-gnu
GTK_LIBS:INTERNAL=
GTK_LIBS_L:INTERNAL=
GTK_LIBS_OTHER:INTERNAL=
GTK_LIBS_PATHS:INTERNAL=
GTK_MODULE_NAME:INTERNAL=gtk+-3.0
GTK_PREFIX:INTERNAL=/snap/flutter/current/usr
GTK_STATIC_CFLAGS:INTERNAL=-pthread;-I/snap/flutter/current/usr/include/gtk-3.0;-I/snap/flutter/current/usr/include/at-spi2-atk/2.0;-I/snap/flutter/current/usr/include/at-spi-2.0;-I/snap/flutter/current/usr/include/dbus-1.0;-I/snap/flutter/current/usr/lib/x86_64-linux-gnu/dbus-1.0/include;-I/snap/flutter/current/usr/include/gtk-3.0;-I/snap/flutter/current/usr/include/gio-unix-2.0;-I/snap/flutter/current/usr/include/cairo;-I/snap/flutter/current/usr/include/pango-1.0;-I/snap/flutter/current/usr/include/fribidi;-I/snap/flutter/current/usr/include/harfbuzz;-I/snap/flutter/current/usr/include/atk-1.0;-I/snap/flutter/current/usr/include/cairo;-I/snap/flutter/current/usr/include/pixman-1;-I/snap/flutter/current/usr/include/uuid;-I/snap/flutter/current/usr/include/freetype2;-I/snap/flutter/current/usr/include/libpng16;-I/snap/flutter/current/usr/include/gdk-pixbuf-2.0;-I/snap/flutter/current/usr/include/libmount;-I/snap/flutter/current/usr/include/blkid;-I/snap/flutter/current/usr/include/glib-2.0;-I/snap/flutter/current/usr/lib/x86_64-linux-gnu/glib-2.0/include
GTK_STATIC_CFLAGS_I:INTERNAL=
GTK_STATIC_CFLAGS_OTHER:INTERNAL=-pthread
GTK_STATIC_INCLUDE_DIRS:INTERNAL=/snap/flutter/current/usr/include/gtk-3.0;/snap/flutter/current/usr/include/at-spi2-atk/2.0;/snap/flutter/current/usr/include/at-spi-2.0;/snap/flutter/current/usr/include/dbus-1.0;/snap/flutter/current/usr/lib/x86_64-linux-gnu/dbus-1.0/include;/snap/flutter/current/usr/include/gtk-3.0;/snap/flutter/current/usr/include/gio-unix-2.0;/snap/flutter/current/usr/include/cairo;/snap/flutter/current/usr/include/pango-1.0;/snap/flutter/current/usr/include/fribidi;/snap/flutter/current/usr/include/harfbuzz;/snap/flutter/current/usr/include/atk-1.0;/snap/flutter/current/usr/include/cairo;/snap/flutter/current/usr/include/pixman-1;/snap/flutter/current/usr/include/uuid;/snap/flutter/current/usr/include/freetype2;/snap/flutter/current/usr/include/libpng16;/snap/flutter/current/usr/include/gdk-pixbuf-2.0;/snap/flutter/current/usr/include/libmount;/snap/flutter/current/usr/include/blkid;/snap/flutter/current/usr/include/glib-2.0;/snap/flutter/current/usr/lib/x86_64-linux-gnu/glib-2.0/include
GTK_STATIC_LDFLAGS:INTERNAL=-L/snap/flutter/current/usr/lib/x86_64-linux-gnu;-L/snap/flutter/current/usr/lib;-L/snap/flutter/current/usr/lib/x86_64-linux-gnu;-lgtk-3;-latk-bridge-2.0;-latspi;-lXtst;-ldbus-1;-lpthread;-lsystemd;-Wl,--export-dynamic;-lgdk-3;-lXinerama;-lXi;-lXrandr;-lXcursor;-lXcomposite;-lXdamage;-lXfixes;-lxkbcommon;-lwayland-cursor;-lwayland-egl;-lwayland-client;-lepoxy;-ldl;-lGL;-lEGL;-lpangocairo-1.0;-lm;-lpangoft2-1.0;-lm;-lpango-1.0;-lm;-lfribidi;-lthai;-ldatrie;-lXft;-lharfbuzz;-lm;-lgraphite2;-latk-1.0;-lcairo-gobject;-lcairo;-lz;-lpixman-1;-lfontconfig;-luuid;-lexpat;-lfreetype;-lpng16;-lm;-lz;-lm;-lxcb-shm;-lxcb-render;-lXrender;-lXext;-lX11;-lpthread;-lxcb;-lXau;-lXdmcp;-lgdk_pixbuf-2.0;-lm;-lgio-2.0;-ldl;-pthread;-lresolv;-lgmodule-2.0;-pthread;-ldl;-lz;-lmount;-lblkid;-lselinux;-lsepol;-lpcre2-8;-pthread;-lgobject-2.0;-pthread;-lffi;-lglib-2.0;-pthread;-lpcre;-pthread
GTK_STATIC_LDFLAGS_OTHER:INTERNAL=-Wl,--export-dynamic;-pthread
GTK_STATIC_LIBDIR:INTERNAL=
GTK_STATIC_LIBRARIES:INTERNAL=gtk-3;atk-bridge-2.0;atspi;Xtst;dbus-1;pthread;systemd;gdk-3;Xinerama;Xi;Xrandr;Xcursor;Xcomposite;Xdamage;Xfixes;xkbcommon;wayland-cursor;wayland-egl;wayland-client;epoxy;dl;GL;EGL;pangocairo-1.0;m;pangoft2-1.0;m;pango-1.0;m;fribidi;thai;datrie;Xft;harfbuzz;m;graphite2;atk-1.0;cairo-gobject;cairo;z;pixman-1;fontconfig;uuid;expat;freetype;png16;m;z;m;xcb-shm;xcb-render;Xrender;Xext;X11;pthread;xcb;Xau;Xdmcp;gdk_pixbuf-2.0;m;gio-2.0;dl;resolv;gmodule-2.0;dl;z;mount;blkid;selinux;sepol;pcre2-8;gobject-2.0;ffi;glib-2.0;pcre
GTK_STATIC_LIBRARY_DIRS:INTERNAL=/snap/flutter/current/usr/lib/x86_64-linux-gnu;/snap/flutter/current/usr/lib;/snap/flutter/current/usr/lib/x86_64-linux-gnu
GTK_STATIC_LIBS:INTERNAL=
GTK_STATIC_LIBS_L:INTERNAL=
GTK_STATIC_LIBS_OTHER:INTERNAL=
GTK_STATIC_LIBS_PATHS:INTERNAL=
GTK_VERSION:INTERNAL=3.24.20
GTK_gtk+-3.0_INCLUDEDIR:INTERNAL=
GTK_gtk+-3.0_LIBDIR:INTERNAL=
GTK_gtk+-3.0_PREFIX:INTERNAL=
GTK_gtk+-3.0_VERSION:INTERNAL=
LIBSECRET_CFLAGS:INTERNAL=-pthread;-I/snap/flutter/current/usr/include/libsecret-1;-I/snap/flutter/current/usr/include/libmount;-I/snap/flutter/current/usr/include/blkid;-I/snap/flutter/current/usr/include/glib-2.0;-I/snap/flutter/current/usr/lib/x86_64-linux-gnu/glib-2.0/include
LIBSECRET_CFLAGS_I:INTERNAL=
LIBSECRET_CFLAGS_OTHER:INTERNAL=-pthread
LIBSECRET_FOUND:INTERNAL=1
LIBSECRET_INCLUDEDIR:INTERNAL=/snap/flutter/current/usr/include
LIBSECRET_INCLUDE_DIRS:INTERNAL=/snap/flutter/current/usr/include/libsecret-1;/snap/flutter/current/usr/include/libmount;/snap/flutter/current/usr/include/blkid;/snap/flutter/current/usr/include/glib-2.0;/snap/flutter/current/usr/lib/x86_64-linux-gnu/glib-2.0/include
LIBSECRET_LDFLAGS:INTERNAL=-L/snap/flutter/current/usr/lib/x86_64-linux-gnu;-lsecret-1;-lgio-2.0;-lgobject-2.0;-lglib-2.0
LIBSECRET_LDFLAGS_OTHER:INTERNAL=
LIBSECRET_LIBDIR:INTERNAL=/snap/flutter/current/usr/lib/x86_64-linux-gnu
LIBSECRET_LIBRARIES:INTERNAL=secret-1;gio-2.0;gobject-2.0;glib-2.0
LIBSECRET_LIBRARY_DIRS:INTERNAL=/snap/flutter/current/usr/lib/x86_64-linux-gnu
LIBSECRET_LIBS:INTERNAL=
LIBSECRET_LIBS_L:INTERNAL=
LIBSECRET_LIBS_OTHER:INTERNAL=
LIBSECRET_LIBS_PATHS:INTERNAL=
LIBSECRET_MODULE_NAME:INTERNAL=libsecret-1
LIBSECRET_PREFIX:INTERNAL=/snap/flutter/current/usr
LIBSECRET_STATIC_CFLAGS:INTERNAL=-pthread;-I/snap/flutter/current/usr/include/libsecret-1;-I/snap/flutter/current/usr/include/libmount;-I/snap/flutter/current/usr/include/blkid;-I/snap/flutter/current/usr/include/glib-2.0;-I/snap/flutter/current/usr/lib/x86_64-linux-gnu/glib-2.0/include
LIBSECRET_STATIC_CFLAGS_I:INTERNAL=
LIBSECRET_STATIC_CFLAGS_OTHER:INTERNAL=-pthread
LIBSECRET_STATIC_INCLUDE_DIRS:INTERNAL=/snap/flutter/current/usr/include/libsecret-1;/snap/flutter/current/usr/include/libmount;/snap/flutter/current/usr/include/blkid;/snap/flutter/current/usr/include/glib-2.0;/snap/flutter/current/usr/lib/x86_64-linux-gnu/glib-2.0/include
LIBSECRET_STATIC_LDFLAGS:INTERNAL=-L/snap/flutter/current/usr/lib/x86_64-linux-gnu;-L/snap/flutter/current/usr/lib;-L/snap/flutter/current/usr/lib/x86_64-linux-gnu;-lsecret-1;-lgcrypt;-lgio-2.0;-ldl;-pthread;-lresolv;-lgmodule-2.0;-pthread;-ldl;-lz;-lmount;-lblkid;-lselinux;-lsepol;-lpcre2-8;-pthread;-lgobject-2.0;-pthread;-lffi;-lglib-2.0;-pthread;-lpcre;-pthread
LIBSECRET_STATIC_LDFLAGS_OTHER:INTERNAL=-pthread
LIBSECRET_STATIC_LIBDIR:INTERNAL=
LIBSECRET_STATIC_LIBRARIES:INTERNAL=secret-1;gcrypt;gio-2.0;dl;resolv;gmodule-2.0;dl;z;mount;blkid;selinux;sepol;pcre2-8;gobject-2.0;ffi;glib-2.0;pcre
LIBSECRET_STATIC_LIBRARY_DIRS:INTERNAL=/snap/flutter/current/usr/lib/x86_64-linux-gnu;/snap/flutter/current/usr/lib;/snap/flutter/current/usr/lib/x86_64-linux-gnu
LIBSECRET_STATIC_LIBS:INTERNAL=
LIBSECRET_STATIC_LIBS_L:INTERNAL=
LIBSECRET_STATIC_LIBS_OTHER:INTERNAL=
LIBSECRET_STATIC_LIBS_PATHS:INTERNAL=
LIBSECRET_VERSION:INTERNAL=0.20.4
LIBSECRET_libsecret-1_INCLUDEDIR:INTERNAL=
LIBSECRET_libsecret-1_LIBDIR:INTERNAL=
LIBSECRET_libsecret-1_PREFIX:INTERNAL=
LIBSECRET_libsecret-1_VERSION:INTERNAL=
//ADVANCED property for variable: PKG_CONFIG_EXECUTABLE
PKG_CONFIG_EXECUTABLE-ADVANCED:INTERNAL=1
__pkg_config_arguments_GIO:INTERNAL=REQUIRED;IMPORTED_TARGET;gio-2.0
__pkg_config_arguments_GLIB:INTERNAL=REQUIRED;IMPORTED_TARGET;glib-2.0
__pkg_config_arguments_GTK:INTERNAL=REQUIRED;IMPORTED_TARGET;gtk+-3.0
__pkg_config_arguments_LIBSECRET:INTERNAL=REQUIRED;IMPORTED_TARGET;libsecret-1>=0.18.4
__pkg_config_checked_GIO:INTERNAL=1
__pkg_config_checked_GLIB:INTERNAL=1
__pkg_config_checked_GTK:INTERNAL=1
__pkg_config_checked_LIBSECRET:INTERNAL=1
//ADVANCED property for variable: pkgcfg_lib_GIO_gio-2.0
pkgcfg_lib_GIO_gio-2.0-ADVANCED:INTERNAL=1
//ADVANCED property for variable: pkgcfg_lib_GIO_glib-2.0
pkgcfg_lib_GIO_glib-2.0-ADVANCED:INTERNAL=1
//ADVANCED property for variable: pkgcfg_lib_GIO_gobject-2.0
pkgcfg_lib_GIO_gobject-2.0-ADVANCED:INTERNAL=1
//ADVANCED property for variable: pkgcfg_lib_GLIB_glib-2.0
pkgcfg_lib_GLIB_glib-2.0-ADVANCED:INTERNAL=1
//ADVANCED property for variable: pkgcfg_lib_GTK_atk-1.0
pkgcfg_lib_GTK_atk-1.0-ADVANCED:INTERNAL=1
//ADVANCED property for variable: pkgcfg_lib_GTK_cairo
pkgcfg_lib_GTK_cairo-ADVANCED:INTERNAL=1
//ADVANCED property for variable: pkgcfg_lib_GTK_cairo-gobject
pkgcfg_lib_GTK_cairo-gobject-ADVANCED:INTERNAL=1
//ADVANCED property for variable: pkgcfg_lib_GTK_gdk-3
pkgcfg_lib_GTK_gdk-3-ADVANCED:INTERNAL=1
//ADVANCED property for variable: pkgcfg_lib_GTK_gdk_pixbuf-2.0
pkgcfg_lib_GTK_gdk_pixbuf-2.0-ADVANCED:INTERNAL=1
//ADVANCED property for variable: pkgcfg_lib_GTK_gio-2.0
pkgcfg_lib_GTK_gio-2.0-ADVANCED:INTERNAL=1
//ADVANCED property for variable: pkgcfg_lib_GTK_glib-2.0
pkgcfg_lib_GTK_glib-2.0-ADVANCED:INTERNAL=1
//ADVANCED property for variable: pkgcfg_lib_GTK_gobject-2.0
pkgcfg_lib_GTK_gobject-2.0-ADVANCED:INTERNAL=1
//ADVANCED property for variable: pkgcfg_lib_GTK_gtk-3
pkgcfg_lib_GTK_gtk-3-ADVANCED:INTERNAL=1
//ADVANCED property for variable: pkgcfg_lib_GTK_harfbuzz
pkgcfg_lib_GTK_harfbuzz-ADVANCED:INTERNAL=1
//ADVANCED property for variable: pkgcfg_lib_GTK_pango-1.0
pkgcfg_lib_GTK_pango-1.0-ADVANCED:INTERNAL=1
//ADVANCED property for variable: pkgcfg_lib_GTK_pangocairo-1.0
pkgcfg_lib_GTK_pangocairo-1.0-ADVANCED:INTERNAL=1
//ADVANCED property for variable: pkgcfg_lib_LIBSECRET_gio-2.0
pkgcfg_lib_LIBSECRET_gio-2.0-ADVANCED:INTERNAL=1
//ADVANCED property for variable: pkgcfg_lib_LIBSECRET_glib-2.0
pkgcfg_lib_LIBSECRET_glib-2.0-ADVANCED:INTERNAL=1
//ADVANCED property for variable: pkgcfg_lib_LIBSECRET_gobject-2.0
pkgcfg_lib_LIBSECRET_gobject-2.0-ADVANCED:INTERNAL=1
//ADVANCED property for variable: pkgcfg_lib_LIBSECRET_secret-1
pkgcfg_lib_LIBSECRET_secret-1-ADVANCED:INTERNAL=1
prefix_result:INTERNAL=/snap/flutter/current/usr/lib/x86_64-linux-gnu

