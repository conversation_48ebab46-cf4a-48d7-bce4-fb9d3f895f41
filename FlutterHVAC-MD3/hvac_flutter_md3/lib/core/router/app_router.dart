import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../features/dashboard/presentation/dashboard_screen.dart';
import '../../features/auth/presentation/login_screen.dart';
import '../../features/customer_management/presentation/customer_profile_widget.dart';
import '../../features/job_scheduling/presentation/job_calendar_widget.dart';
import '../../features/analytics/presentation/analytics_dashboard_widget.dart';
import '../../features/equipment_management/presentation/equipment_details_widget.dart';
import '../../features/office_management/presentation/office_task_widget.dart';

final routerProvider = Provider<GoRouter>((ref) {
  return GoRouter(
    initialLocation: '/dashboard',
    routes: [
      // Auth Routes
      GoRoute(
        path: '/login',
        name: 'login',
        builder: (context, state) => const LoginScreen(),
      ),
      
      // Main App Routes
      GoRoute(
        path: '/dashboard',
        name: 'dashboard',
        builder: (context, state) => const DashboardScreen(),
      ),
      // Customer Management
      GoRoute(
        path: '/customers/:customerId',
        name: 'customers',
        builder: (context, state) {
          final customerId = state.pathParameters['customerId'];
          return CustomerProfileWidget(customerId: customerId);
        },
      ),
      // Job Scheduling
      GoRoute(
        path: '/jobs',
        name: 'jobs',
        builder: (context, state) => const JobCalendarWidget(),
      ),
      // Analytics and Reporting
      GoRoute(
        path: '/analytics',
        name: 'analytics',
        builder: (context, state) => const AnalyticsDashboardWidget(),
      ),
      // Equipment Management
      GoRoute(
        path: '/equipment/:equipmentId',
        name: 'equipment',
        builder: (context, state) {
          final equipmentId = state.pathParameters['equipmentId'];
          return EquipmentDetailsWidget(equipmentId: equipmentId);
        },
      ),
      // Office Management
      GoRoute(
        path: '/office/:taskName/:taskDescription/:dueDate',
        name: 'office',
        builder: (context, state) {
          final taskName = state.pathParameters['taskName'];
          final taskDescription = state.pathParameters['taskDescription'];
          final dueDateString = state.pathParameters['dueDate'];
          
          DateTime? dueDate;
          if (dueDateString != null) {
            try {
              dueDate = DateTime.parse(dueDateString);
            } catch (e) {
              // Handle parsing error, e.g., log it or set a default
              print('Error parsing dueDate: $e');
            }
          }

          return OfficeTaskWidget(
            taskName: taskName,
            taskDescription: taskDescription,
            dueDate: dueDate,
          );
        },
      ),
    ],
  );
});