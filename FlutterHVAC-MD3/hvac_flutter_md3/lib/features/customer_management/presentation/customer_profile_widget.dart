import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../shared/providers/customer_provider.dart';
import '../../../shared/widgets/md3_components/crm_data_card.dart';
import '../../../shared/models/customer_model.dart';

class CustomerProfileWidget extends ConsumerWidget {
  final String? customerId;

  const CustomerProfileWidget({
    Key? key,
    this.customerId,
  }) : super(key: key);

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final customerAsyncValue = ref.watch(customerByIdProvider(customerId ?? ''));

    return customerAsyncValue.when(
      data: (customer) => SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          children: [
            CRMDataCard(
              title: customer.name,
              content: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text('Email: ${customer.email}'),
                  Text('Phone: ${customer.phone}'),
                  if (customer.address != null) Text('Address: ${customer.address}'),
                  if (customer.type != null) Text('Type: ${customer.type}'),
                  if (customer.status != null) Text('Status: ${customer.status}'),
                  Text('Created: ${customer.createdAt.toLocal().toShortDateString()}'),
                  Text('Last Updated: ${customer.updatedAt.toLocal().toShortDateString()}'),
                  if (customer.lastInteractionDate != null)
                    Text('Last Interaction: ${customer.lastInteractionDate!.toLocal().toShortDateString()}'),
                ],
              ),
              actions: [
                ElevatedButton(
                  onPressed: () {
                    // TODO: Implement navigation to edit customer screen/dialog
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(content: Text('Edit Customer functionality not yet implemented.')),
                    );
                  },
                  child: const Text('Edit'),
                ),
              ],
            ),
            // TODO: Add more sections like documents, jobs history, etc.
          ],
        ),
      ),
      loading: () => const Center(child: CircularProgressIndicator()),
      error: (error, stack) => Center(child: Text('Error: ${error.toString()}')),
    );
  }
}

// Extension for DateTime to format date
extension DateTimeExtension on DateTime {
  String toShortDateString() {
    return '${year}-${month.toString().padLeft(2, '0')}-${day.toString().padLeft(2, '0')}';
  }
}
