import 'package:flutter/material.dart';
import 'package:table_calendar/table_calendar.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:intl/intl.dart'; // For date formatting
import '../../../../shared/providers/job_event_provider.dart';
import '../../../../shared/models/job_event_model.dart';

class JobCalendarWidget extends ConsumerStatefulWidget {
  const JobCalendarWidget({Key? key}) : super(key: key);

  @override
  ConsumerState<JobCalendarWidget> createState() => _JobCalendarWidgetState();
}

class _JobCalendarWidgetState extends ConsumerState<JobCalendarWidget> {
  late final ValueNotifier<List<JobEvent>> _selectedEvents;
  CalendarFormat _calendarFormat = CalendarFormat.month;
  DateTime _focusedDay = DateTime.now();
  DateTime? _selectedDay;

  @override
  void initState() {
    super.initState();
    _selectedDay = _focusedDay;
    _selectedEvents = ValueNotifier(_getEventsForDay(_selectedDay!));
  }

  @override
  void dispose() {
    _selectedEvents.dispose();
    super.dispose();
  }

  List<JobEvent> _getEventsForDay(DateTime day) {
    final allEvents = ref.watch(jobEventProvider);
    return allEvents.where((event) => isSameDay(event.date, day)).toList();
  }

  void _onDaySelected(DateTime selectedDay, DateTime focusedDay) {
    if (!isSameDay(_selectedDay, selectedDay)) {
      setState(() {
        _selectedDay = selectedDay;
        _focusedDay = focusedDay;
        _calendarFormat = CalendarFormat.week; // Switch to week view on day selection
      });
      _selectedEvents.value = _getEventsForDay(selectedDay);
    }
  }
  
  void _onFormatChanged(CalendarFormat format) {
    if (_calendarFormat != format) {
      setState(() {
        _calendarFormat = format;
      });
    }
  }

  void _onPageChanged(DateTime focusedDay) {
    setState(() {
      _focusedDay = focusedDay;
    });
  }

  @override
  Widget build(BuildContext context) {
    final allEvents = ref.watch(jobEventProvider); // Ensure the widget rebuilds when events change
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;

    return Scaffold(
      appBar: AppBar(
        title: const Text('Job Calendar'),
        actions: [
          IconButton(
            icon: const Icon(Icons.add_task_outlined),
            onPressed: () {
              // Logic for adding a new job event
            },
            tooltip: 'Add new job',
          )
        ],
      ),
      body: Column(
        children: [
          TableCalendar<JobEvent>(
            firstDay: DateTime.utc(DateTime.now().year - 1, DateTime.now().month, 1),
            lastDay: DateTime.utc(DateTime.now().year + 1, DateTime.now().month, 31),
            focusedDay: _focusedDay,
            selectedDayPredicate: (day) => isSameDay(_selectedDay, day),
            calendarFormat: _calendarFormat,
            eventLoader: (day) {
              return allEvents.where((event) => isSameDay(event.date, day)).toList();
            },
            startingDayOfWeek: StartingDayOfWeek.monday,
            headerStyle: HeaderStyle(
              formatButtonTextStyle: TextStyle(color: colorScheme.onPrimary),
              formatButtonDecoration: BoxDecoration(
                color: colorScheme.primary,
                borderRadius: BorderRadius.circular(20.0),
              ),
              titleCentered: true,
              titleTextStyle: textTheme.titleLarge!.copyWith(color: colorScheme.primary),
              leftChevronIcon: Icon(Icons.chevron_left, color: colorScheme.primary),
              rightChevronIcon: Icon(Icons.chevron_right, color: colorScheme.primary),
            ),
            calendarStyle: CalendarStyle(
              todayDecoration: BoxDecoration(
                color: colorScheme.primary.withOpacity(0.3),
                shape: BoxShape.circle,
              ),
              selectedDecoration: BoxDecoration(
                color: colorScheme.primary,
                shape: BoxShape.circle,
              ),
              markerDecoration: BoxDecoration(
                color: colorScheme.secondary,
                shape: BoxShape.circle,
              ),
              markersMaxCount: 1, // Show a single dot for days with events
              outsideDaysVisible: false,
            ),
            onDaySelected: _onDaySelected,
            onFormatChanged: _onFormatChanged,
            onPageChanged: _onPageChanged,
          ),
          const SizedBox(height: 12.0),
          Expanded(
            child: ValueListenableBuilder<List<JobEvent>>(
              valueListenable: _selectedEvents,
              builder: (context, value, _) {
                if (value.isEmpty) {
                  return Center(
                    child: Text(
                      'No jobs for the selected day.',
                      style: textTheme.titleMedium?.copyWith(color: colorScheme.onSurfaceVariant),
                    ),
                  );
                }
                return ListView.builder(
                  itemCount: value.length,
                  itemBuilder: (context, index) {
                    final event = value[index];
                    return Card(
                      margin: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 6.0),
                      elevation: 1.5,
                      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
                      child: ListTile(
                        leading: CircleAvatar(
                          backgroundColor: colorScheme.secondaryContainer,
                          child: Text(DateFormat('HH:mm').format(event.date), 
                                 style: textTheme.labelSmall?.copyWith(color: colorScheme.onSecondaryContainer)),
                        ),
                        title: Text(event.title, 
                               style: textTheme.titleMedium?.copyWith(fontWeight: FontWeight.w600)),
                        subtitle: event.description != null ? Text(event.description!, 
                                 style: textTheme.bodyMedium) : null,
                        onTap: () {
                          // Navigate to job details
                          print('Selected event: ${event.title}');
                        },
                      ),
                    );
                  },
                );
              },
            ),
          ),
        ],
      ),
    );
  }
}
