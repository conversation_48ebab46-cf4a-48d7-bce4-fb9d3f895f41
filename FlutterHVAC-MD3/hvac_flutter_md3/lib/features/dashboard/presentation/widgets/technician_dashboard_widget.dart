import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

// Placeholder for a potential provider if needed later for state management
// final technicianDashboardProvider = Provider((ref) => ...);

class TechnicianDashboardWidget extends ConsumerWidget {
  const TechnicianDashboardWidget({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final colorScheme = Theme.of(context).colorScheme;
    // final textTheme = Theme.of(context).textTheme; // For easy access to text styles

    return Scaffold(
      appBar: AppBar(
        title: const Text('Panel Technika'),
        // Potencjalne akcje w AppBar, np. odświeżanie, filtry
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: () {
              // Logika odświeżania danych
            },
            tooltip: 'Od<PERSON>wież dane',
          ),
          IconButton(
            icon: const Icon(Icons.filter_list),
            onPressed: () {
              // Logika otwierania filtrów
            },
            tooltip: 'Filtruj widok',
          ),
        ],
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: ListView(
          children: <Widget>[
            // Sekcja: Pilne zadania / Powiadomienia
            _buildSectionHeader(context, 'Pilne Zadania / Alerty'),
            _buildPlaceholderCard(
              context,
              title: 'Alert: Krytyczna awaria - Jednostka AC-1023',
              subtitle: 'Klient: Biurowiec Alpha, ul. Słoneczna 5',
              icon: Icons.warning_amber_rounded,
              iconColor: colorScheme.error,
            ),
            const SizedBox(height: 24),

            // Sekcja: Dzisiejsze zlecenia
            _buildSectionHeader(context, 'Dzisiejsze Zlecenia (3)'),
            _buildPlaceholderCard(
              context,
              title: 'Przegląd okresowy - Jednostka HVAC-001',
              subtitle: 'Klient: Jan Kowalski, ul. Leśna 12, godz. 10:00',
              icon: Icons.calendar_today_outlined,
            ),
            _buildPlaceholderCard(
              context,
              title: 'Naprawa usterki - Pompa ciepła XYZ',
              subtitle: 'Klient: Firma Delta, ul. Przemysłowa 1, godz. 14:00',
              icon: Icons.build_circle_outlined,
            ),
            const SizedBox(height: 24),

            // Sekcja: Status monitorowanych urządzeń
            _buildSectionHeader(context, 'Status Urządzeń'),
            // Tutaj docelowo będą interaktywne karty statusu sprzętu
            // Na razie dodamy kilka przykładów
            _buildEquipmentStatusCard(
              context,
              deviceName: 'Klimatyzator AX-45 (Biuro 101)',
              status: 'Online - Działa poprawnie',
              statusColor: Colors.green.shade700,
              details: 'Temp: 22°C, Wilgotność: 45%',
            ),
            _buildEquipmentStatusCard(
              context,
              deviceName: 'Pompa ciepła GeoTerm (Serwerownia)',
              status: 'Offline - Wymaga uwagi',
              statusColor: colorScheme.error,
              details: 'Ostatnie połączenie: 2 godz. temu',
            ),
             _buildEquipmentStatusCard(
              context,
              deviceName: 'Wentylator VentoMax (Magazyn)',
              status: 'Online - Przegląd za 7 dni',
              statusColor: colorScheme.tertiary, // Using tertiary for a less critical warning
              details: 'Czas pracy: 1250h',
            ),
            // Dodaj więcej kart w miarę potrzeb
          ],
        ),
      ),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: () {
          // Logika dodawania nowego zadania lub szybkiej akcji
        },
        label: const Text('Szybkie Zlecenie'),
        icon: const Icon(Icons.add_task_outlined),
        // backgroundColor: colorScheme.primary,
        // foregroundColor: colorScheme.onPrimary,
      ),
    );
  }

  Widget _buildSectionHeader(BuildContext context, String title) {
    final textTheme = Theme.of(context).textTheme;
    final colorScheme = Theme.of(context).colorScheme;
    return Padding(
      padding: const EdgeInsets.only(bottom: 8.0, top: 16.0),
      child: Text(
        title.toUpperCase(),
        style: textTheme.titleSmall?.copyWith(
          color: colorScheme.primary, // Or colorScheme.onSurfaceVariant
          fontWeight: FontWeight.bold,
          letterSpacing: 0.8,
        ),
      ),
    );
  }

  Widget _buildPlaceholderCard(BuildContext context, {required String title, required String subtitle, IconData? icon, Color? iconColor}) {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;

    return Card(
      elevation: 2, // MD3 typically uses lower elevation or surface tints
      // shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)), // Defined in theme
      child: ListTile(
        leading: icon != null ? Icon(icon, color: iconColor ?? colorScheme.primary, size: 36) : null,
        title: Text(title, style: textTheme.titleMedium?.copyWith(fontWeight: FontWeight.w600)),
        subtitle: Text(subtitle, style: textTheme.bodyMedium),
        trailing: Icon(Icons.arrow_forward_ios_rounded, size: 16, color: colorScheme.outline),
        onTap: () {
          // Logika nawigacji do szczegółów zadania/alertu
        },
      ),
    );
  }

  Widget _buildEquipmentStatusCard(BuildContext context, {required String deviceName, required String status, required Color statusColor, String? details}) {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;

    return Card(
      // elevation: 1, // Subtle elevation
      // surfaceTintColor: statusColor.withOpacity(0.1), // Surface tint based on status
      margin: const EdgeInsets.symmetric(vertical: 6.0),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(deviceName, style: textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold)),
            const SizedBox(height: 8),
            Row(
              children: [
                Icon(Icons.circle, color: statusColor, size: 12),
                const SizedBox(width: 8),
                Expanded(child: Text(status, style: textTheme.bodyMedium?.copyWith(color: statusColor))),
              ],
            ),
            if (details != null && details.isNotEmpty) ...[
              const SizedBox(height: 8),
              Text(details, style: textTheme.bodySmall?.copyWith(color: colorScheme.onSurfaceVariant)),
            ],
            const SizedBox(height: 12),
            Align(
              alignment: Alignment.centerRight,
              child: TextButton(
                onPressed: () {
                  // Logika nawigacji do szczegółów urządzenia
                },
                child: const Text('Zobacz Szczegóły'),
              ),
            ),
          ],
        ),
      ),
    );
  }
}