import 'package:flutter/material.dart';
import 'package:fl_chart/fl_chart.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../shared/providers/analytics_provider.dart';
import '../../../../shared/models/analytics_model.dart';
import 'dart:math' show max;

class AnalyticsDashboardWidget extends ConsumerWidget {
  const AnalyticsDashboardWidget({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final analyticsAsyncValue = ref.watch(analyticsDataProvider);
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;
    
    // Predefined list of colors for charts
    final List<MaterialColor> chartColors = [
      Colors.blue, Colors.green, Colors.red, Colors.orange,
      Colors.purple, Colors.teal, Colors.pink, Colors.amber
    ];

    return Scaffold(
      appBar: AppBar(
        title: const Text('Analytics Dashboard'),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: () {
              ref.invalidate(analyticsDataProvider); // Invalidate to refetch data
            },
            tooltip: 'Refresh data',
          ),
        ],
      ),
      body: analyticsAsyncValue.when(
        data: (analyticsState) {
          if (analyticsState.isEmpty) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(Icons.insights_rounded, size: 60, color: colorScheme.outline),
                  const SizedBox(height: 16),
                  Text('No analytics data available', style: textTheme.titleMedium),
                  const SizedBox(height: 8),
                  Text('Try refreshing or check your configuration', style: textTheme.bodySmall),
                ],
              ),
            );
          }
          return ListView.builder(
            padding: const EdgeInsets.all(16.0),
            itemCount: analyticsState.length,
            itemBuilder: (context, index) {
              final dataSet = analyticsState[index];
              final List<FlSpot> spots = dataSet.values
                  .asMap()
                  .entries
                  .map((entry) => FlSpot(entry.key.toDouble(), entry.value))
                  .toList();
              
              final lineColor = chartColors[index % chartColors.length];

              return Card(
                elevation: 2,
                margin: const EdgeInsets.only(bottom: 20.0),
                shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        dataSet.metricName,
                        style: textTheme.titleLarge?.copyWith(
                          color: colorScheme.primary,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      const SizedBox(height: 20),
                      SizedBox(
                        height: 200,
                        child: LineChart(
                          LineChartData(
                            gridData: FlGridData(
                              show: true,
                              drawVerticalLine: true,
                              getDrawingHorizontalLine: (value) {
                                return FlLine(
                                  color: colorScheme.outline.withOpacity(0.2),
                                  strokeWidth: 1,
                                );
                              },
                              getDrawingVerticalLine: (value) {
                                return FlLine(
                                  color: colorScheme.outline.withOpacity(0.2),
                                  strokeWidth: 1,
                                );
                              },
                            ),
                            titlesData: FlTitlesData(
                              show: true,
                              bottomTitles: AxisTitles(
                                sideTitles: SideTitles(
                                  showTitles: true,
                                  reservedSize: 22,
                                  getTitlesWidget: (value, meta) {
                                    // Use labels from dataSet if available, otherwise use numeric labels
                                    if (dataSet.labels != null && value.toInt() < dataSet.labels!.length) {
                                      return Text(dataSet.labels![value.toInt()], style: textTheme.bodySmall?.copyWith(color: colorScheme.onSurfaceVariant));
                                    }
                                    return Text((value.toInt() + 1).toString(), style: textTheme.bodySmall?.copyWith(color: colorScheme.onSurfaceVariant));
                                  },
                                  interval: 1,
                                  // margin: 8, // margin is now part of AxisTitles
                                ),
                              ),
                              leftTitles: AxisTitles(
                                sideTitles: SideTitles(
                                  showTitles: true,
                                  getTitlesWidget: (value, meta) {
                                    if (value % 1 == 0) return Text(value.toInt().toString(), style: textTheme.bodySmall?.copyWith(color: colorScheme.onSurfaceVariant));
                                    return const Text('');
                                  },
                                  reservedSize: 28,
                                  interval: 1,
                                  // margin: 12, // margin is now part of AxisTitles
                                ),
                              ),
                              topTitles: AxisTitles(sideTitles: SideTitles(showTitles: false)),
                              rightTitles: AxisTitles(sideTitles: SideTitles(showTitles: false)),
                            ),
                            borderData: FlBorderData(
                              show: true,
                              border: Border.all(color: colorScheme.outline.withOpacity(0.3), width: 1),
                            ),
                            minX: 0,
                            maxX: (spots.length - 1).toDouble(),
                            minY: 0,
                            maxY: spots.isEmpty ? 10 : spots.map((s) => s.y).reduce(max) + 1,
                            lineBarsData: [
                              LineChartBarData(
                                spots: spots,
                                isCurved: true,
                                gradient: LinearGradient(
                                  colors: [lineColor, lineColor.shade300],
                                  begin: Alignment.centerLeft,
                                  end: Alignment.centerRight,
                                ),
                                barWidth: 4,
                                isStrokeCapRound: true,
                                dotData: FlDotData(
                                  show: true,
                                  getDotPainter: (spot, percent, barData, index) {
                                    return FlDotCirclePainter(
                                      radius: 4,
                                      color: lineColor.shade700,
                                      strokeWidth: 1.5,
                                      strokeColor: colorScheme.surface,
                                    );
                                  },
                                ),
                                belowBarData: BarAreaData(
                                  show: true,
                                  gradient: LinearGradient(
                                    colors: [
                                      lineColor.withOpacity(0.3),
                                      lineColor.withOpacity(0.0)
                                    ],
                                    stops: const [0.5, 1.0],
                                    begin: Alignment.topCenter,
                                    end: Alignment.bottomCenter,
                                  ),
                                  // gradientColorStops and gradientFrom/To are now part of LinearGradient
                                ),
                              ),
                            ],
                            lineTouchData: LineTouchData(
                              touchTooltipData: LineTouchTooltipData(
                                getTooltipItems: (List<LineBarSpot> touchedBarSpots) {
                                  return touchedBarSpots.map((barSpot) {
                                    final flSpot = barSpot;
                                    return LineTooltipItem(
                                      '${dataSet.metricName}: ${flSpot.y.toStringAsFixed(2)}\n',
                                      textTheme.bodyMedium!.copyWith(color: colorScheme.onPrimaryContainer, fontWeight: FontWeight.bold),
                                      children: [
                                        TextSpan(
                                          text: 'Period: ${dataSet.labels != null && flSpot.x.toInt() < dataSet.labels!.length ? dataSet.labels![flSpot.x.toInt()] : (flSpot.x + 1).toInt()}',
                                          style: textTheme.bodySmall!.copyWith(color: colorScheme.onPrimaryContainer),
                                        ),
                                      ],
                                    );
                                  }).toList();
                                }
                              ),
                              handleBuiltInTouches: true,
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              );
            },
          );
        },
        loading: () => const Center(child: CircularProgressIndicator()),
        error: (error, stack) => Center(child: Text('Error: ${error.toString()}')),
      ),
    );
  }
}
