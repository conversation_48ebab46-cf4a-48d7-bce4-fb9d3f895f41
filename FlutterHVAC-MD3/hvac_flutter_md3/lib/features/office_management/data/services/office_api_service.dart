import 'package:dio/dio.dart';
import 'package:logger/logger.dart';
import '../models/employee_model.dart';
import '../models/department_model.dart';
import '../models/office_task_model.dart';
import '../models/document_model.dart';

class OfficeApiService {
  static const String _baseUrl = 'http://localhost:8080/api/v1'; // GoBackend API endpoint
  
  final Dio _dio;
  final Logger _logger;

  OfficeApiService({
    Dio? dio,
    Logger? logger,
  }) : _dio = dio ?? Dio(),
        _logger = logger ?? Logger() {
    _setupInterceptors();
  }

  void _setupInterceptors() {
    _dio.interceptors.add(
      InterceptorsWrapper(
        onRequest: (options, handler) {
          options.headers['Content-Type'] = 'application/json';
          _logger.d('API Request: ${options.method} ${options.uri}');
          handler.next(options);
        },
        onResponse: (response, handler) {
          _logger.d('API Response: ${response.statusCode} ${response.requestOptions.uri}');
          handler.next(response);
        },
        onError: (error, handler) {
          _logger.e('API Error: ${error.message}');
          handler.next(error);
        },
      ),
    );
  }

  // Employee Management
  Future<List<Employee>> getEmployees({
    String? departmentId,
    EmployeeStatus? status,
    int page = 1,
    int limit = 20,
  }) async {
    try {
      final queryParams = <String, dynamic>{
        'page': page,
        'limit': limit,
      };
      
      if (departmentId != null) queryParams['department_id'] = departmentId;
      if (status != null) queryParams['status'] = status.name;

      final response = await _dio.get(
        '$_baseUrl/employees',
        queryParameters: queryParams,
      );

      final List<dynamic> data = response.data['employees'] ?? [];
      return data.map((json) => Employee.fromJson(json)).toList();
    } catch (e) {
      _logger.e('Failed to get employees: $e');
      rethrow;
    }
  }

  Future<Employee> getEmployee(String id) async {
    try {
      final response = await _dio.get('$_baseUrl/employees/$id');
      return Employee.fromJson(response.data);
    } catch (e) {
      _logger.e('Failed to get employee $id: $e');
      rethrow;
    }
  }

  Future<Employee> createEmployee(Employee employee) async {
    try {
      final response = await _dio.post(
        '$_baseUrl/employees',
        data: employee.toJson(),
      );
      return Employee.fromJson(response.data);
    } catch (e) {
      _logger.e('Failed to create employee: $e');
      rethrow;
    }
  }

  Future<Employee> updateEmployee(Employee employee) async {
    try {
      final response = await _dio.put(
        '$_baseUrl/employees/${employee.id}',
        data: employee.toJson(),
      );
      return Employee.fromJson(response.data);
    } catch (e) {
      _logger.e('Failed to update employee ${employee.id}: $e');
      rethrow;
    }
  }

  Future<void> deleteEmployee(String id) async {
    try {
      await _dio.delete('$_baseUrl/employees/$id');
    } catch (e) {
      _logger.e('Failed to delete employee $id: $e');
      rethrow;
    }
  }  // Department Management
  Future<List<Department>> getDepartments() async {
    try {
      final response = await _dio.get('$_baseUrl/departments');
      final List<dynamic> data = response.data['departments'] ?? [];
      return data.map((json) => Department.fromJson(json)).toList();
    } catch (e) {
      _logger.e('Failed to get departments: $e');
      rethrow;
    }
  }

  Future<Department> getDepartment(String id) async {
    try {
      final response = await _dio.get('$_baseUrl/departments/$id');
      return Department.fromJson(response.data);
    } catch (e) {
      _logger.e('Failed to get department $id: $e');
      rethrow;
    }
  }

  Future<Department> createDepartment(Department department) async {
    try {
      final response = await _dio.post(
        '$_baseUrl/departments',
        data: department.toJson(),
      );
      return Department.fromJson(response.data);
    } catch (e) {
      _logger.e('Failed to create department: $e');
      rethrow;
    }
  }

  // Task Management
  Future<List<OfficeTask>> getTasks({
    String? assignedToId,
    String? departmentId,
    TaskStatus? status,
    TaskPriority? priority,
    int page = 1,
    int limit = 20,
  }) async {
    try {
      final queryParams = <String, dynamic>{
        'page': page,
        'limit': limit,
      };
      
      if (assignedToId != null) queryParams['assigned_to_id'] = assignedToId;
      if (departmentId != null) queryParams['department_id'] = departmentId;
      if (status != null) queryParams['status'] = status.name;
      if (priority != null) queryParams['priority'] = priority.name;

      final response = await _dio.get(
        '$_baseUrl/tasks',
        queryParameters: queryParams,
      );

      final List<dynamic> data = response.data['tasks'] ?? [];
      return data.map((json) => OfficeTask.fromJson(json)).toList();
    } catch (e) {
      _logger.e('Failed to get tasks: $e');
      rethrow;
    }
  }

  Future<OfficeTask> createTask(OfficeTask task) async {
    try {
      final response = await _dio.post(
        '$_baseUrl/tasks',
        data: task.toJson(),
      );
      return OfficeTask.fromJson(response.data);
    } catch (e) {
      _logger.e('Failed to create task: $e');
      rethrow;
    }
  }

  Future<OfficeTask> updateTask(OfficeTask task) async {
    try {
      final response = await _dio.put(
        '$_baseUrl/tasks/${task.id}',
        data: task.toJson(),
      );
      return OfficeTask.fromJson(response.data);
    } catch (e) {
      _logger.e('Failed to update task ${task.id}: $e');
      rethrow;
    }
  }

  // Document Management
  Future<List<Document>> getDocuments({
    DocumentCategory? category,
    String? uploadedById,
    int page = 1,
    int limit = 20,
  }) async {
    try {
      final queryParams = <String, dynamic>{
        'page': page,
        'limit': limit,
      };
      
      if (category != null) queryParams['category'] = category.name;
      if (uploadedById != null) queryParams['uploaded_by_id'] = uploadedById;

      final response = await _dio.get(
        '$_baseUrl/documents',
        queryParameters: queryParams,
      );

      final List<dynamic> data = response.data['documents'] ?? [];
      return data.map((json) => Document.fromJson(json)).toList();
    } catch (e) {
      _logger.e('Failed to get documents: $e');
      rethrow;
    }
  }

  Future<Document> uploadDocument({
    required String filePath,
    required String title,
    String? description,
    required DocumentCategory category,
    List<String>? tags,
    bool isConfidential = false,
  }) async {
    try {
      final formData = FormData.fromMap({
        'file': await MultipartFile.fromFile(filePath),
        'title': title,
        'description': description,
        'category': category.name,
        'tags': tags?.join(','),
        'is_confidential': isConfidential,
      });

      final response = await _dio.post(
        '$_baseUrl/documents',
        data: formData,
      );

      return Document.fromJson(response.data);
    } catch (e) {
      _logger.e('Failed to upload document: $e');
      rethrow;
    }
  }
}