import 'package:dio/dio.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:logger/logger.dart';

class KratosAuthService {
  static const String _baseUrl = 'http://localhost:4433'; // Kratos public endpoint
  static const String _adminUrl = 'http://localhost:4434'; // Kratos admin endpoint
  
  final Dio _dio;
  final FlutterSecureStorage _secureStorage;
  final Logger _logger;

  KratosAuthService({
    Dio? dio,
    FlutterSecureStorage? secureStorage,
    Logger? logger,
  }) : _dio = dio ?? Dio(),
        _secureStorage = secureStorage ?? const FlutterSecureStorage(),
        _logger = logger ?? Logger() {
    _setupInterceptors();
  }

  void _setupInterceptors() {
    _dio.interceptors.add(
      InterceptorsWrapper(
        onRequest: (options, handler) async {
          // Add session token if available
          final sessionToken = await getSessionToken();
          if (sessionToken != null) {
            options.headers['Authorization'] = 'Bearer $sessionToken';
          }
          
          // Add CSRF token for state-changing operations
          if (['POST', 'PUT', 'PATCH', 'DELETE'].contains(options.method)) {
            final csrfToken = await getCsrfToken();
            if (csrfToken != null) {
              options.headers['X-CSRF-Token'] = csrfToken;
            }
          }
          
          _logger.d('Request: ${options.method} ${options.uri}');
          handler.next(options);
        },
        onResponse: (response, handler) {
          _logger.d('Response: ${response.statusCode} ${response.requestOptions.uri}');
          handler.next(response);
        },
        onError: (error, handler) {
          _logger.e('Error: ${error.message}');
          handler.next(error);
        },
      ),
    );
  }

  // Session Management
  Future<String?> getSessionToken() async {
    return await _secureStorage.read(key: 'kratos_session_token');
  }

  Future<void> setSessionToken(String token) async {
    await _secureStorage.write(key: 'kratos_session_token', value: token);
  }

  Future<void> clearSessionToken() async {
    await _secureStorage.delete(key: 'kratos_session_token');
  }

  Future<String?> getCsrfToken() async {
    return await _secureStorage.read(key: 'kratos_csrf_token');
  }

  Future<void> setCsrfToken(String token) async {
    await _secureStorage.write(key: 'kratos_csrf_token', value: token);
  }
  // Authentication Flow Methods
  Future<Map<String, dynamic>> initializeLoginFlow() async {
    try {
      final response = await _dio.get('$_baseUrl/self-service/login/api');
      return response.data;
    } catch (e) {
      _logger.e('Failed to initialize login flow: $e');
      rethrow;
    }
  }

  Future<Map<String, dynamic>> submitLoginFlow({
    required String flowId,
    required String identifier,
    required String password,
  }) async {
    try {
      final response = await _dio.post(
        '$_baseUrl/self-service/login',
        queryParameters: {'flow': flowId},
        data: {
          'method': 'password',
          'identifier': identifier,
          'password': password,
        },
      );
      
      // Extract session token from response
      final sessionToken = response.data['session_token'];
      if (sessionToken != null) {
        await setSessionToken(sessionToken);
      }
      
      return response.data;
    } catch (e) {
      _logger.e('Failed to submit login flow: $e');
      rethrow;
    }
  }

  Future<Map<String, dynamic>> initializeRegistrationFlow() async {
    try {
      final response = await _dio.get('$_baseUrl/self-service/registration/api');
      return response.data;
    } catch (e) {
      _logger.e('Failed to initialize registration flow: $e');
      rethrow;
    }
  }

  Future<Map<String, dynamic>> submitRegistrationFlow({
    required String flowId,
    required String email,
    required String password,
    required String firstName,
    required String lastName,
  }) async {
    try {
      final response = await _dio.post(
        '$_baseUrl/self-service/registration',
        queryParameters: {'flow': flowId},
        data: {
          'method': 'password',
          'traits.email': email,
          'traits.first_name': firstName,
          'traits.last_name': lastName,
          'password': password,
        },
      );
      
      return response.data;
    } catch (e) {
      _logger.e('Failed to submit registration flow: $e');
      rethrow;
    }
  }

  // Session Validation
  Future<Map<String, dynamic>?> getCurrentSession() async {
    try {
      final response = await _dio.get('$_baseUrl/sessions/whoami');
      return response.data;
    } catch (e) {
      _logger.e('Failed to get current session: $e');
      return null;
    }
  }

  Future<bool> isSessionValid() async {
    final session = await getCurrentSession();
    return session != null && session['active'] == true;
  }

  // Logout
  Future<void> logout() async {
    try {
      await _dio.delete('$_baseUrl/self-service/logout/api');
      await clearSessionToken();
    } catch (e) {
      _logger.e('Failed to logout: $e');
      // Clear local session even if API call fails
      await clearSessionToken();
    }
  }

  // Profile Management
  Future<Map<String, dynamic>> getProfile() async {
    try {
      final response = await _dio.get('$_baseUrl/self-service/settings/api');
      return response.data;
    } catch (e) {
      _logger.e('Failed to get profile: $e');
      rethrow;
    }
  }

  Future<Map<String, dynamic>> updateProfile({
    required String flowId,
    required Map<String, dynamic> traits,
  }) async {
    try {
      final response = await _dio.post(
        '$_baseUrl/self-service/settings',
        queryParameters: {'flow': flowId},
        data: {
          'method': 'profile',
          'traits': traits,
        },
      );
      
      return response.data;
    } catch (e) {
      _logger.e('Failed to update profile: $e');
      rethrow;
    }
  }
}