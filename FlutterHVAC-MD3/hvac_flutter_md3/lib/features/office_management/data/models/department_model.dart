import 'package:json_annotation/json_annotation.dart';

part 'department_model.g.dart';

@JsonSerializable()
class Department {
  final String id;
  final String name;
  final String description;
  final String? managerId;
  final String? managerName;
  final int employeeCount;
  final List<String> employeeIds;
  final DepartmentType type;
  final String? location;
  final double? budget;
  final DateTime createdAt;
  final DateTime updatedAt;

  Department({
    required this.id,
    required this.name,
    required this.description,
    this.managerId,
    this.managerName,
    required this.employeeCount,
    required this.employeeIds,
    required this.type,
    this.location,
    this.budget,
    required this.createdAt,
    required this.updatedAt,
  });

  // JSON Serialization
  factory Department.fromJson(Map<String, dynamic> json) => _$DepartmentFromJson(json);
  Map<String, dynamic> toJson() => _$DepartmentToJson(this);

  // Copy with method for updates
  Department copyWith({
    String? id,
    String? name,
    String? description,
    String? managerId,
    String? managerName,
    int? employeeCount,
    List<String>? employeeIds,
    DepartmentType? type,
    String? location,
    double? budget,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return Department(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      managerId: managerId ?? this.managerId,
      managerName: managerName ?? this.managerName,
      employeeCount: employeeCount ?? this.employeeCount,
      employeeIds: employeeIds ?? this.employeeIds,
      type: type ?? this.type,
      location: location ?? this.location,
      budget: budget ?? this.budget,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }
}

@JsonEnum()
enum DepartmentType {
  @JsonValue('operations')
  operations,
  @JsonValue('sales')
  sales,
  @JsonValue('customer_service')
  customerService,
  @JsonValue('administration')
  administration,
  @JsonValue('technical')
  technical,
  @JsonValue('management')
  management,
}

extension DepartmentTypeExtension on DepartmentType {
  String get displayName {
    switch (this) {
      case DepartmentType.operations:
        return 'Operations';
      case DepartmentType.sales:
        return 'Sales';
      case DepartmentType.customerService:
        return 'Customer Service';
      case DepartmentType.administration:
        return 'Administration';
      case DepartmentType.technical:
        return 'Technical';
      case DepartmentType.management:
        return 'Management';
    }
  }
}