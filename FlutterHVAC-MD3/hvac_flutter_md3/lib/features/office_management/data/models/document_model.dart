import 'package:json_annotation/json_annotation.dart';

part 'document_model.g.dart';

@JsonSerializable()
class Document {
  final String id;
  final String title;
  final String? description;
  final String fileName;
  final String fileUrl;
  final String fileType;
  final int fileSize;
  final DocumentCategory category;
  final DocumentStatus status;
  final String uploadedById;
  final String? uploadedByName;
  final List<String> tags;
  final List<String> sharedWithIds;
  final DateTime? expiryDate;
  final bool isConfidential;
  final int version;
  final String? parentDocumentId;
  final DateTime createdAt;
  final DateTime updatedAt;

  Document({
    required this.id,
    required this.title,
    this.description,
    required this.fileName,
    required this.fileUrl,
    required this.fileType,
    required this.fileSize,
    required this.category,
    required this.status,
    required this.uploadedById,
    this.uploadedByName,
    required this.tags,
    required this.sharedWithIds,
    this.expiryDate,
    required this.isConfidential,
    required this.version,
    this.parentDocumentId,
    required this.createdAt,
    required this.updatedAt,
  });

  bool get isExpired => expiryDate != null && expiryDate!.isBefore(DateTime.now());
  bool get isExpiringSoon => expiryDate != null && 
      expiryDate!.isAfter(DateTime.now()) && 
      expiryDate!.isBefore(DateTime.now().add(const Duration(days: 30)));

  String get fileSizeFormatted {
    if (fileSize < 1024) return '${fileSize}B';
    if (fileSize < 1024 * 1024) return '${(fileSize / 1024).toStringAsFixed(1)}KB';
    return '${(fileSize / (1024 * 1024)).toStringAsFixed(1)}MB';
  }

  // JSON Serialization
  factory Document.fromJson(Map<String, dynamic> json) => _$DocumentFromJson(json);
  Map<String, dynamic> toJson() => _$DocumentToJson(this);
}

@JsonEnum()
enum DocumentCategory {
  @JsonValue('contract')
  contract,
  @JsonValue('invoice')
  invoice,
  @JsonValue('report')
  report,
  @JsonValue('certification')
  certification,
  @JsonValue('policy')
  policy,
  @JsonValue('manual')
  manual,
  @JsonValue('form')
  form,
  @JsonValue('other')
  other,
}

@JsonEnum()
enum DocumentStatus {
  @JsonValue('draft')
  draft,
  @JsonValue('active')
  active,
  @JsonValue('archived')
  archived,
  @JsonValue('expired')
  expired,
}