// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'office_task_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

OfficeTask _$OfficeTaskFromJson(Map<String, dynamic> json) => OfficeTask(
      id: json['id'] as String,
      title: json['title'] as String,
      description: json['description'] as String,
      priority: $enumDecode(_$TaskPriorityEnumMap, json['priority']),
      status: $enumDecode(_$TaskStatusEnumMap, json['status']),
      assignedToId: json['assignedToId'] as String,
      assignedToName: json['assignedToName'] as String?,
      createdById: json['createdById'] as String,
      createdByName: json['createdByName'] as String?,
      dueDate: DateTime.parse(json['dueDate'] as String),
      completedAt: json['completedAt'] == null
          ? null
          : DateTime.parse(json['completedAt'] as String),
      tags: (json['tags'] as List<dynamic>).map((e) => e as String).toList(),
      comments: (json['comments'] as List<dynamic>)
          .map((e) => TaskComment.fromJson(e as Map<String, dynamic>))
          .toList(),
      attachments: (json['attachments'] as List<dynamic>)
          .map((e) => TaskAttachment.fromJson(e as Map<String, dynamic>))
          .toList(),
      estimatedHours: (json['estimatedHours'] as num?)?.toDouble(),
      actualHours: (json['actualHours'] as num?)?.toDouble(),
      departmentId: json['departmentId'] as String?,
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: DateTime.parse(json['updatedAt'] as String),
    );

Map<String, dynamic> _$OfficeTaskToJson(OfficeTask instance) =>
    <String, dynamic>{
      'id': instance.id,
      'title': instance.title,
      'description': instance.description,
      'priority': _$TaskPriorityEnumMap[instance.priority]!,
      'status': _$TaskStatusEnumMap[instance.status]!,
      'assignedToId': instance.assignedToId,
      'assignedToName': instance.assignedToName,
      'createdById': instance.createdById,
      'createdByName': instance.createdByName,
      'dueDate': instance.dueDate.toIso8601String(),
      'completedAt': instance.completedAt?.toIso8601String(),
      'tags': instance.tags,
      'comments': instance.comments,
      'attachments': instance.attachments,
      'estimatedHours': instance.estimatedHours,
      'actualHours': instance.actualHours,
      'departmentId': instance.departmentId,
      'createdAt': instance.createdAt.toIso8601String(),
      'updatedAt': instance.updatedAt.toIso8601String(),
    };

const _$TaskPriorityEnumMap = {
  TaskPriority.low: 'low',
  TaskPriority.medium: 'medium',
  TaskPriority.high: 'high',
  TaskPriority.urgent: 'urgent',
};

const _$TaskStatusEnumMap = {
  TaskStatus.todo: 'todo',
  TaskStatus.inProgress: 'in_progress',
  TaskStatus.review: 'review',
  TaskStatus.completed: 'completed',
  TaskStatus.cancelled: 'cancelled',
};

TaskComment _$TaskCommentFromJson(Map<String, dynamic> json) => TaskComment(
      id: json['id'] as String,
      content: json['content'] as String,
      authorId: json['authorId'] as String,
      authorName: json['authorName'] as String?,
      createdAt: DateTime.parse(json['createdAt'] as String),
    );

Map<String, dynamic> _$TaskCommentToJson(TaskComment instance) =>
    <String, dynamic>{
      'id': instance.id,
      'content': instance.content,
      'authorId': instance.authorId,
      'authorName': instance.authorName,
      'createdAt': instance.createdAt.toIso8601String(),
    };

TaskAttachment _$TaskAttachmentFromJson(Map<String, dynamic> json) =>
    TaskAttachment(
      id: json['id'] as String,
      fileName: json['fileName'] as String,
      fileUrl: json['fileUrl'] as String,
      fileType: json['fileType'] as String,
      fileSize: (json['fileSize'] as num).toInt(),
      uploadedById: json['uploadedById'] as String,
      uploadedAt: DateTime.parse(json['uploadedAt'] as String),
    );

Map<String, dynamic> _$TaskAttachmentToJson(TaskAttachment instance) =>
    <String, dynamic>{
      'id': instance.id,
      'fileName': instance.fileName,
      'fileUrl': instance.fileUrl,
      'fileType': instance.fileType,
      'fileSize': instance.fileSize,
      'uploadedById': instance.uploadedById,
      'uploadedAt': instance.uploadedAt.toIso8601String(),
    };
