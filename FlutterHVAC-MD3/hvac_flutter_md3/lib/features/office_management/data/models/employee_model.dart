import 'package:json_annotation/json_annotation.dart';

part 'employee_model.g.dart';

@JsonSerializable()
class Employee {
  final String id;
  final String firstName;
  final String lastName;
  final String email;
  final String phone;
  final String? avatar;
  final String department;
  final String position;
  final EmployeeRole role;
  final EmployeeStatus status;
  final DateTime hireDate;
  final String? address;
  final double? hourlyRate;
  final List<String> skills;
  final String? emergencyContact;
  final String? emergencyPhone;
  final DateTime createdAt;
  final DateTime updatedAt;

  Employee({
    required this.id,
    required this.firstName,
    required this.lastName,
    required this.email,
    required this.phone,
    this.avatar,
    required this.department,
    required this.position,
    required this.role,
    required this.status,
    required this.hireDate,
    this.address,
    this.hourlyRate,
    required this.skills,
    this.emergencyContact,
    this.emergencyPhone,
    required this.createdAt,
    required this.updatedAt,
  });

  String get fullName => '$firstName $lastName';

  String get initials => '${firstName.isNotEmpty ? firstName[0] : ''}${lastName.isNotEmpty ? lastName[0] : ''}';

  bool get isActive => status == EmployeeStatus.active;

  // JSON Serialization
  factory Employee.fromJson(Map<String, dynamic> json) => _$EmployeeFromJson(json);
  Map<String, dynamic> toJson() => _$EmployeeToJson(this);

  // Copy with method for updates
  Employee copyWith({
    String? id,
    String? firstName,
    String? lastName,
    String? email,
    String? phone,
    String? avatar,
    String? department,
    String? position,
    EmployeeRole? role,
    EmployeeStatus? status,
    DateTime? hireDate,
    String? address,
    double? hourlyRate,
    List<String>? skills,
    String? emergencyContact,
    String? emergencyPhone,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return Employee(
      id: id ?? this.id,
      firstName: firstName ?? this.firstName,
      lastName: lastName ?? this.lastName,
      email: email ?? this.email,
      phone: phone ?? this.phone,
      avatar: avatar ?? this.avatar,
      department: department ?? this.department,
      position: position ?? this.position,
      role: role ?? this.role,
      status: status ?? this.status,
      hireDate: hireDate ?? this.hireDate,
      address: address ?? this.address,
      hourlyRate: hourlyRate ?? this.hourlyRate,
      skills: skills ?? this.skills,
      emergencyContact: emergencyContact ?? this.emergencyContact,
      emergencyPhone: emergencyPhone ?? this.emergencyPhone,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }
}

@JsonEnum()
enum EmployeeRole {
  @JsonValue('admin')
  admin,
  @JsonValue('manager')
  manager,
  @JsonValue('technician')
  technician,
  @JsonValue('office_staff')
  officeStaff,
  @JsonValue('sales')
  sales,
  @JsonValue('customer_service')
  customerService,
}

@JsonEnum()
enum EmployeeStatus {
  @JsonValue('active')
  active,
  @JsonValue('inactive')
  inactive,
  @JsonValue('on_leave')
  onLeave,
  @JsonValue('terminated')
  terminated,
}

extension EmployeeRoleExtension on EmployeeRole {
  String get displayName {
    switch (this) {
      case EmployeeRole.admin:
        return 'Administrator';
      case EmployeeRole.manager:
        return 'Manager';
      case EmployeeRole.technician:
        return 'Technician';
      case EmployeeRole.officeStaff:
        return 'Office Staff';
      case EmployeeRole.sales:
        return 'Sales Representative';
      case EmployeeRole.customerService:
        return 'Customer Service';
    }
  }
}

extension EmployeeStatusExtension on EmployeeStatus {
  String get displayName {
    switch (this) {
      case EmployeeStatus.active:
        return 'Active';
      case EmployeeStatus.inactive:
        return 'Inactive';
      case EmployeeStatus.onLeave:
        return 'On Leave';
      case EmployeeStatus.terminated:
        return 'Terminated';
    }
  }
}