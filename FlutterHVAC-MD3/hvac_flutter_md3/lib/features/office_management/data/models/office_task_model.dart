import 'package:json_annotation/json_annotation.dart';

part 'office_task_model.g.dart';

@JsonSerializable()
class OfficeTask {
  final String id;
  final String title;
  final String description;
  final TaskPriority priority;
  final TaskStatus status;
  final String assignedToId;
  final String? assignedToName;
  final String createdById;
  final String? createdByName;
  final DateTime dueDate;
  final DateTime? completedAt;
  final List<String> tags;
  final List<TaskComment> comments;
  final List<TaskAttachment> attachments;
  final double? estimatedHours;
  final double? actualHours;
  final String? departmentId;
  final DateTime createdAt;
  final DateTime updatedAt;

  OfficeTask({
    required this.id,
    required this.title,
    required this.description,
    required this.priority,
    required this.status,
    required this.assignedToId,
    this.assignedToName,
    required this.createdById,
    this.createdByName,
    required this.dueDate,
    this.completedAt,
    required this.tags,
    required this.comments,
    required this.attachments,
    this.estimatedHours,
    this.actualHours,
    this.departmentId,
    required this.createdAt,
    required this.updatedAt,
  });

  bool get isOverdue => dueDate.isBefore(DateTime.now()) && status != TaskStatus.completed;
  bool get isCompleted => status == TaskStatus.completed;
  int get daysUntilDue => dueDate.difference(DateTime.now()).inDays;

  // JSON Serialization
  factory OfficeTask.fromJson(Map<String, dynamic> json) => _$OfficeTaskFromJson(json);
  Map<String, dynamic> toJson() => _$OfficeTaskToJson(this);
}

@JsonSerializable()
class TaskComment {
  final String id;
  final String content;
  final String authorId;
  final String? authorName;
  final DateTime createdAt;

  TaskComment({
    required this.id,
    required this.content,
    required this.authorId,
    this.authorName,
    required this.createdAt,
  });

  factory TaskComment.fromJson(Map<String, dynamic> json) => _$TaskCommentFromJson(json);
  Map<String, dynamic> toJson() => _$TaskCommentToJson(this);
}

@JsonSerializable()
class TaskAttachment {
  final String id;
  final String fileName;
  final String fileUrl;
  final String fileType;
  final int fileSize;
  final String uploadedById;
  final DateTime uploadedAt;

  TaskAttachment({
    required this.id,
    required this.fileName,
    required this.fileUrl,
    required this.fileType,
    required this.fileSize,
    required this.uploadedById,
    required this.uploadedAt,
  });

  factory TaskAttachment.fromJson(Map<String, dynamic> json) => _$TaskAttachmentFromJson(json);
  Map<String, dynamic> toJson() => _$TaskAttachmentToJson(this);
}

@JsonEnum()
enum TaskPriority {
  @JsonValue('low')
  low,
  @JsonValue('medium')
  medium,
  @JsonValue('high')
  high,
  @JsonValue('urgent')
  urgent,
}

@JsonEnum()
enum TaskStatus {
  @JsonValue('todo')
  todo,
  @JsonValue('in_progress')
  inProgress,
  @JsonValue('review')
  review,
  @JsonValue('completed')
  completed,
  @JsonValue('cancelled')
  cancelled,
}