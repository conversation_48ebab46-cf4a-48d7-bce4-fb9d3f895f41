import 'package:flutter/material.dart';

class OfficeTaskWidget extends StatelessWidget {
  final String? taskName;
  final String? taskDescription;
  final DateTime? dueDate;

  const OfficeTaskWidget({
    Key? key,
    this.taskName,
    this.taskDescription,
    this.dueDate,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.all(16.0),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              taskName ?? 'N/A',
              style: Theme.of(context).textTheme.headlineSmall,
            ),
            const SizedBox(height: 8.0),
            Text(
              taskDescription ?? 'N/A',
              style: Theme.of(context).textTheme.bodyMedium,
            ),
            const SizedBox(height: 8.0),
            Text(
              'Due: ${dueDate?.toLocal().toString().split(' ')[0] ?? 'N/A'}',
              style: Theme.of(context).textTheme.bodySmall,
            ),
          ],
        ),
      ),
    );
  }
}
