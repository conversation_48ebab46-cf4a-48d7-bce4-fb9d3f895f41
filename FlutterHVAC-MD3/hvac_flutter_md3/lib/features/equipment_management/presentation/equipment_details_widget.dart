import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../shared/providers/equipment_provider.dart';
import '../../../shared/widgets/md3_components/crm_data_card.dart';
import '../../../shared/models/equipment_status_model.dart';
import '../../../shared/models/customer_model.dart'; // For customer name lookup
import '../../../shared/providers/customer_provider.dart'; // For customer name lookup

class EquipmentDetailsWidget extends ConsumerWidget {
  final String? equipmentId;

  const EquipmentDetailsWidget({
    Key? key,
    this.equipmentId,
  }) : super(key: key);

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final equipmentAsyncValue = ref.watch(equipmentByIdProvider(equipmentId ?? ''));

    return equipmentAsyncValue.when(
      data: (equipment) {
        final customerAsyncValue = equipment.customerId != null
            ? ref.watch(customerByIdProvider(equipment.customerId!))
            : null;

        return SingleChildScrollView(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            children: [
              CRMDataCard(
                title: equipment.deviceName,
                content: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text('Model: ${equipment.modelNumber ?? 'N/A'}'),
                    Text('Location: ${equipment.location ?? 'N/A'}'),
                    Row(
                      children: [
                        Icon(equipment.statusIcon, color: equipment.statusColor),
                        const SizedBox(width: 8),
                        Text('Status: ${equipment.statusDetails}'),
                      ],
                    ),
                    if (equipment.lastSeen != null)
                      Text('Last Seen: ${equipment.lastSeen!.toLocal().toShortDateString()}'),
                    if (equipment.installationDate != null)
                      Text('Installation Date: ${equipment.installationDate!.toLocal().toShortDateString()}'),
                    if (equipment.customerId != null)
                      customerAsyncValue!.when(
                        data: (customer) => Text('Customer: ${customer.name}'),
                        loading: () => const Text('Customer: Loading...'),
                        error: (error, stack) => Text('Customer: Error loading (${error.toString()})'),
                      ),
                    if (equipment.telemetry != null && equipment.telemetry!.isNotEmpty)
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const SizedBox(height: 8),
                          Text('Telemetry Data:', style: Theme.of(context).textTheme.titleSmall),
                          ...equipment.telemetry!.entries.map((entry) => Text('${entry.key}: ${entry.value}')),
                        ],
                      ),
                    if (equipment.maintenanceHistory != null && equipment.maintenanceHistory!.isNotEmpty)
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const SizedBox(height: 8),
                          Text('Maintenance History:', style: Theme.of(context).textTheme.titleSmall),
                          ...equipment.maintenanceHistory!.map((history) => Text('- $history')),
                        ],
                      ),
                  ],
                ),
                actions: [
                  ElevatedButton(
                    onPressed: () {
                      // TODO: Implement navigation to edit equipment screen/dialog
                      ScaffoldMessenger.of(context).showSnackBar(
                        const SnackBar(content: Text('Edit Equipment functionality not yet implemented.')),
                      );
                    },
                    child: const Text('Edit'),
                  ),
                ],
              ),
              // TODO: Add more sections like related jobs, documents, etc.
            ],
          ),
        );
      },
      loading: () => const Center(child: CircularProgressIndicator()),
      error: (error, stack) => Center(child: Text('Error: ${error.toString()}')),
    );
  }
}

// Re-using the extension from customer_profile_widget.dart
extension DateTimeExtension on DateTime {
  String toShortDateString() {
    return '${year}-${month.toString().padLeft(2, '0')}-${day.toString().padLeft(2, '0')}';
  }
}