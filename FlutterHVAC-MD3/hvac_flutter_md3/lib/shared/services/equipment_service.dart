import 'dart:convert';
import 'package:dio/dio.dart';
import 'package:logger/logger.dart';
import '../models/equipment_status_model.dart';

class EquipmentService {
  final Dio _dio;
  final Logger _logger;
  final String baseUrl;

  EquipmentService({
    required this.baseUrl,
    Dio? dio,
    Logger? logger,
  })  : _dio = dio ?? Dio(),
        _logger = logger ?? Logger();

  Future<List<EquipmentStatus>> fetchEquipmentStatuses({
    String? customerId,
    OperationalStatus? status,
    int page = 1,
    int limit = 20,
  }) async {
    try {
      final Map<String, dynamic> queryParams = {
        'page': page,
        'limit': limit,
      };
      if (customerId != null) queryParams['customerId'] = customerId;
      if (status != null) queryParams['status'] = status.name;

      final response = await _dio.get(
        '$baseUrl/equipment',
        queryParameters: queryParams,
      );

      final List<dynamic> equipmentListJson = response.data['equipment'] ?? [];
      return equipmentListJson.map((json) => EquipmentStatus.fromJson(json)).toList();
    } on DioException catch (e) {
      _logger.e('Failed to load equipment statuses: ${e.message}');
      rethrow;
    }
  }

  Future<EquipmentStatus> fetchEquipmentById(String equipmentId) async {
    try {
      final response = await _dio.get('$baseUrl/equipment/$equipmentId');
      return EquipmentStatus.fromJson(response.data);
    } on DioException catch (e) {
      _logger.e('Failed to load equipment $equipmentId: ${e.message}');
      rethrow;
    }
  }

  Future<EquipmentStatus> addEquipmentStatus(EquipmentStatus equipment) async {
    try {
      final response = await _dio.post(
        '$baseUrl/equipment',
        data: equipment.toJson(),
      );
      return EquipmentStatus.fromJson(response.data);
    } on DioException catch (e) {
      _logger.e('Failed to add equipment status: ${e.message}');
      rethrow;
    }
  }

  Future<EquipmentStatus> updateEquipmentStatus(EquipmentStatus equipment) async {
    try {
      final response = await _dio.put(
        '$baseUrl/equipment/${equipment.id}',
        data: equipment.toJson(),
      );
      return EquipmentStatus.fromJson(response.data);
    } on DioException catch (e) {
      _logger.e('Failed to update equipment status: ${e.message}');
      rethrow;
    }
  }

  Future<void> deleteEquipmentStatus(String equipmentId) async {
    try {
      await _dio.delete('$baseUrl/equipment/$equipmentId');
    } on DioException catch (e) {
      _logger.e('Failed to delete equipment status: ${e.message}');
      rethrow;
    }
  }
}
