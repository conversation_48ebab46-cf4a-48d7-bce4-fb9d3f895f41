import 'package:dio/dio.dart';
import 'package:logger/logger.dart';
import '../models/analytics_model.dart';

class AnalyticsService {
  final Dio _dio;
  final Logger _logger;
  final String baseUrl;

  AnalyticsService({
    required this.baseUrl,
    Dio? dio,
    Logger? logger,
  })  : _dio = dio ?? Dio(),
        _logger = logger ?? Logger();

  Future<List<AnalyticsData>> fetchSalesAnalytics({
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    try {
      final Map<String, dynamic> queryParams = {};
      if (startDate != null) queryParams['startDate'] = startDate.toIso8601String();
      if (endDate != null) queryParams['endDate'] = endDate.toIso8601String();

      final response = await _dio.get(
        '$baseUrl/analytics/sales',
        queryParameters: queryParams,
      );

      final List<dynamic> data = response.data['sales_data'] ?? [];
      return data.map((json) => AnalyticsData.fromJson(json)).toList();
    } on DioException catch (e) {
      _logger.e('Failed to fetch sales analytics: ${e.message}');
      rethrow;
    }
  }

  Future<List<AnalyticsData>> fetchCustomerGrowthAnalytics({
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    try {
      final Map<String, dynamic> queryParams = {};
      if (startDate != null) queryParams['startDate'] = startDate.toIso8601String();
      if (endDate != null) queryParams['endDate'] = endDate.toIso8601String();

      final response = await _dio.get(
        '$baseUrl/analytics/customer_growth',
        queryParameters: queryParams,
      );

      final List<dynamic> data = response.data['customer_growth_data'] ?? [];
      return data.map((json) => AnalyticsData.fromJson(json)).toList();
    } on DioException catch (e) {
      _logger.e('Failed to fetch customer growth analytics: ${e.message}');
      rethrow;
    }
  }

  // You can add more methods for other types of analytics data here
  // e.g., fetchJobCompletionRates, fetchEquipmentPerformance
}