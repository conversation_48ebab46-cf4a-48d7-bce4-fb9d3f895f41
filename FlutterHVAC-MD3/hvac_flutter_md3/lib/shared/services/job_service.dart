import 'dart:convert';
import 'package:http/http.dart' as http;
import '../models/job_model.dart';

class JobService {
  final String baseUrl;

  JobService({required this.baseUrl});

  Future<List<Job>> fetchJobs() async {
    final response = await http.get(Uri.parse('$baseUrl/jobs'));

    if (response.statusCode == 200) {
      final List<dynamic> jobListJson = json.decode(response.body);
      return jobListJson.map((json) => Job.fromJson(json)).toList();
    } else {
      throw Exception('Failed to load jobs');
    }
  }

  Future<void> addJob(Job job) async {
    final response = await http.post(
      Uri.parse('$baseUrl/jobs'),
      headers: {'Content-Type': 'application/json'},
      body: json.encode(job.toJson()),
    );

    if (response.statusCode != 201) {
      throw Exception('Failed to add job');
    }
  }

  Future<void> updateJob(Job job) async {
    final response = await http.put(
      Uri.parse('$baseUrl/jobs/${job.id}'),
      headers: {'Content-Type': 'application/json'},
      body: json.encode(job.toJson()),
    );

    if (response.statusCode != 200) {
      throw Exception('Failed to update job');
    }
  }

  Future<void> deleteJob(String jobId) async {
    final response = await http.delete(Uri.parse('$baseUrl/jobs/$jobId'));

    if (response.statusCode != 200) {
      throw Exception('Failed to delete job');
    }
  }
}
