import 'dart:convert';
import 'package:dio/dio.dart';
import 'package:logger/logger.dart';
import '../models/customer_model.dart';
import '../../features/office_management/data/models/document_model.dart'; // Assuming Document model is here

class CustomerService {
  final Dio _dio;
  final Logger _logger;
  final String baseUrl; // e.g., http://localhost:8080/api/v1

  CustomerService({
    required this.baseUrl,
    Dio? dio,
    Logger? logger,
  })  : _dio = dio ?? Dio(),
        _logger = logger ?? Logger();

  Future<List<Customer>> fetchCustomers({
    String? query,
    String? type,
    String? status,
    int page = 1,
    int limit = 20,
  }) async {
    try {
      final Map<String, dynamic> queryParams = {
        'page': page,
        'limit': limit,
      };
      if (query != null) queryParams['query'] = query;
      if (type != null) queryParams['type'] = type;
      if (status != null) queryParams['status'] = status;

      final response = await _dio.get(
        '$baseUrl/customers',
        queryParameters: queryParams,
      );

      final List<dynamic> customerListJson = response.data['customers'] ?? [];
      return customerListJson.map((json) => Customer.fromJson(json)).toList();
    } on DioException catch (e) {
      _logger.e('Failed to load customers: ${e.message}');
      rethrow;
    }
  }

  Future<Customer> fetchCustomerById(String customerId) async {
    try {
      final response = await _dio.get('$baseUrl/customers/$customerId');
      return Customer.fromJson(response.data);
    } on DioException catch (e) {
      _logger.e('Failed to load customer $customerId: ${e.message}');
      rethrow;
    }
  }

  Future<Customer> addCustomer(Customer customer) async {
    try {
      final response = await _dio.post(
        '$baseUrl/customers',
        data: customer.toJson(),
      );
      return Customer.fromJson(response.data);
    } on DioException catch (e) {
      _logger.e('Failed to add customer: ${e.message}');
      rethrow;
    }
  }

  Future<Customer> updateCustomer(Customer customer) async {
    try {
      final response = await _dio.put(
        '$baseUrl/customers/${customer.id}',
        data: customer.toJson(),
      );
      return Customer.fromJson(response.data);
    } on DioException catch (e) {
      _logger.e('Failed to update customer ${customer.id}: ${e.message}');
      rethrow;
    }
  }

  Future<void> deleteCustomer(String customerId) async {
    try {
      await _dio.delete('$baseUrl/customers/$customerId');
    } on DioException catch (e) {
      _logger.e('Failed to delete customer $customerId: ${e.message}');
      rethrow;
    }
  }

  Future<List<Document>> fetchCustomerDocuments(String customerId) async {
    try {
      final response = await _dio.get('$baseUrl/customers/$customerId/documents');
      final List<dynamic> documentListJson = response.data['documents'] ?? [];
      return documentListJson.map((json) => Document.fromJson(json)).toList();
    } on DioException catch (e) {
      _logger.e('Failed to load documents for customer $customerId: ${e.message}');
      rethrow;
    }
  }
}