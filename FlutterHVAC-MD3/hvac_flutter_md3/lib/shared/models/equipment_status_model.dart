import 'package:flutter/material.dart'; // For Color

enum OperationalStatus { online, offline, warning, error, maintenance }

class EquipmentStatus {
  final String id;
  final String deviceName;
  final String? modelNumber;
  final String? location; // e.g., "Biuro 101", "Serwerownia Klienta X"
  final OperationalStatus status;
  final String statusDetails; // e.g., "Działa poprawnie", "Niski poziom czynnika", "Przegląd za 7 dni"
  final DateTime? lastSeen;
  final Map<String, dynamic>? telemetry; // For flexible data like temp, humidity, pressure, etc.
  final String? customerId;
  final String? imageUrl; // Optional image of the equipment
  final DateTime? installationDate; // New field for installation date
  final List<String>? maintenanceHistory; // New field for maintenance history

  EquipmentStatus({
    required this.id,
    required this.deviceName,
    this.modelNumber,
    this.location,
    required this.status,
    required this.statusDetails,
    this.lastSeen,
    this.telemetry,
    this.customerId,
    this.imageUrl,
    this.installationDate,
    this.maintenanceHistory,
  });

  // Helper to get a color based on status for UI representation
  Color get statusColor {
    switch (status) {
      case OperationalStatus.online:
        return Colors.green.shade700;
      case OperationalStatus.warning:
        return Colors.orange.shade600; // More distinct than yellow for warnings
      case OperationalStatus.maintenance:
        return Colors.blue.shade600;
      case OperationalStatus.offline:
        return Colors.grey.shade600;
      case OperationalStatus.error:
        return Colors.red.shade700;
      default:
        return Colors.grey;
    }
  }

  // Helper for an icon representing the status
  IconData get statusIcon {
    switch (status) {
      case OperationalStatus.online:
        return Icons.check_circle_outline;
      case OperationalStatus.warning:
        return Icons.warning_amber_rounded;
      case OperationalStatus.maintenance:
        return Icons.build_outlined;
      case OperationalStatus.offline:
        return Icons.power_off_outlined;
      case OperationalStatus.error:
        return Icons.error_outline;
      default:
        return Icons.help_outline;
    }
  }

  // Example of a copyWith method
  EquipmentStatus copyWith({
    String? id,
    String? deviceName,
    String? modelNumber,
    String? location,
    OperationalStatus? status,
    String? statusDetails,
    DateTime? lastSeen,
    Map<String, dynamic>? telemetry,
    String? customerId,
    String? imageUrl,
    DateTime? installationDate,
    List<String>? maintenanceHistory,
  }) {
    return EquipmentStatus(
      id: id ?? this.id,
      deviceName: deviceName ?? this.deviceName,
      modelNumber: modelNumber ?? this.modelNumber,
      location: location ?? this.location,
      status: status ?? this.status,
      statusDetails: statusDetails ?? this.statusDetails,
      lastSeen: lastSeen ?? this.lastSeen,
      telemetry: telemetry ?? this.telemetry,
      customerId: customerId ?? this.customerId,
      imageUrl: imageUrl ?? this.imageUrl,
      installationDate: installationDate ?? this.installationDate,
      maintenanceHistory: maintenanceHistory ?? this.maintenanceHistory,
    );
  }

  // JSON Serialization
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'deviceName': deviceName,
      'modelNumber': modelNumber,
      'location': location,
      'status': status.name, // Using .name for enums
      'statusDetails': statusDetails,
      'lastSeen': lastSeen?.toIso8601String(),
      'telemetry': telemetry, // Assuming telemetry is already a Map<String, dynamic> or null
      'customerId': customerId,
      'imageUrl': imageUrl,
      'installationDate': installationDate?.toIso8601String(),
      'maintenanceHistory': maintenanceHistory,
    };
  }

  // JSON Deserialization
  factory EquipmentStatus.fromJson(Map<String, dynamic> json) {
    return EquipmentStatus(
      id: json['id'] as String,
      deviceName: json['deviceName'] as String,
      modelNumber: json['modelNumber'] as String?,
      location: json['location'] as String?,
      status: OperationalStatus.values.firstWhere(
          (e) => e.name == json['status'],
          orElse: () => OperationalStatus.offline),
      statusDetails: json['statusDetails'] as String,
      lastSeen: json['lastSeen'] == null
          ? null
          : DateTime.parse(json['lastSeen'] as String),
      telemetry: json['telemetry'] == null
          ? null
          : Map<String, dynamic>.from(json['telemetry'] as Map),
      customerId: json['customerId'] as String?,
      imageUrl: json['imageUrl'] as String?,
      installationDate: json['installationDate'] == null
          ? null
          : DateTime.parse(json['installationDate'] as String),
      maintenanceHistory: (json['maintenanceHistory'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
    );
  }
}
