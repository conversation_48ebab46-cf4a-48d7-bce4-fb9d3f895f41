class Customer {
  final String id;
  final String name;
  final String email;
  final String phone;
  final String? address;
  final DateTime createdAt;
  final DateTime updatedAt;
  final DateTime? lastInteractionDate;
  final String? type; // e.g., 'residential', 'commercial'
  final String? status; // e.g., 'active', 'lead', 'inactive'

  Customer({
    required this.id,
    required this.name,
    required this.email,
    required this.phone,
    this.address,
    required this.createdAt,
    required this.updatedAt,
    this.lastInteractionDate,
    this.type,
    this.status,
  });

  // JSON Serialization
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'email': email,
      'phone': phone,
      'address': address,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
      'last_interaction_date': lastInteractionDate?.toIso8601String(),
      'type': type,
      'status': status,
    };
  }

  // JSON Deserialization
  factory Customer.fromJson(Map<String, dynamic> json) {
    return Customer(
      id: json['id'] as String,
      name: json['name'] as String,
      email: json['email'] as String,
      phone: json['phone'] as String,
      address: json['address'] as String?,
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: DateTime.parse(json['updated_at'] as String),
      lastInteractionDate: json['last_interaction_date'] != null
          ? DateTime.parse(json['last_interaction_date'] as String)
          : null,
      type: json['type'] as String?,
      status: json['status'] as String?,
    );
  }

  Customer copyWith({
    String? id,
    String? name,
    String? email,
    String? phone,
    String? address,
    DateTime? createdAt,
    DateTime? updatedAt,
    DateTime? lastInteractionDate,
    String? type,
    String? status,
  }) {
    return Customer(
      id: id ?? this.id,
      name: name ?? this.name,
      email: email ?? this.email,
      phone: phone ?? this.phone,
      address: address ?? this.address,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      lastInteractionDate: lastInteractionDate ?? this.lastInteractionDate,
      type: type ?? this.type,
      status: status ?? this.status,
    );
  }
}
