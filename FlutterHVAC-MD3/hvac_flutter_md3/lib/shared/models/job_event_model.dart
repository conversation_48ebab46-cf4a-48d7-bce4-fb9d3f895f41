class JobEvent {
  final String id;
  final String title;
  final DateTime date;
  final String? description;

  JobEvent({
    required this.id,
    required this.title,
    required this.date,
    this.description,
  });

  // JSON Serialization
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'date': date.toIso8601String(),
      'description': description,
    };
  }

  // JSON Deserialization
  factory JobEvent.fromJson(Map<String, dynamic> json) {
    return JobEvent(
      id: json['id'] as String,
      title: json['title'] as String,
      date: DateTime.parse(json['date'] as String),
      description: json['description'] as String?,
    );
  }
}
