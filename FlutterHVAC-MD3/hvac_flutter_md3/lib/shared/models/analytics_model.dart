class AnalyticsData {
  final String metricName;
  final List<double> values;
  final List<String>? labels; // New field for x-axis labels (e.g., dates, categories)

  AnalyticsData({
    required this.metricName,
    required this.values,
    this.labels,
  });

  // JSON Serialization
  Map<String, dynamic> toJson() {
    return {
      'metricName': metricName,
      'values': values,
      'labels': labels,
    };
  }

  // JSON Deserialization
  factory AnalyticsData.fromJson(Map<String, dynamic> json) {
    return AnalyticsData(
      metricName: json['metricName'] as String,
      values: List<double>.from(json['values'] as List),
      labels: (json['labels'] as List<dynamic>?)?.map((e) => e as String).toList(),
    );
  }
}
