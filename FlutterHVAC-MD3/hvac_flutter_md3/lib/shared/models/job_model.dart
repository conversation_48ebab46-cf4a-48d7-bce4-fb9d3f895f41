import 'package:flutter/material.dart'; // For potential Color or IconData if status is more complex

enum JobPriority { low, medium, high, critical }
enum JobStatus { pending, inProgress, completed, onHold, cancelled, requiresAttention }

class Job {
  final String id;
  final String title;
  final String description;
  final String? customerName;
  final String? customerAddress;
  final String? assignedTechnicianId; // ID of the technician it's assigned to
  final DateTime? creationDate;
  final DateTime? dueDate;
  final DateTime? scheduledTime;
  final JobPriority priority;
  final JobStatus status;
  final String? equipmentId; // Associated equipment, if any
  final List<String>? notes;
  final String? relatedTicketId; // Link to a Kratos ticket or similar
  final DateTime? actualCompletionDate; // New field for actual completion date
  final List<String>? technicianNotes; // New field for technician-specific notes

  Job({
    required this.id,
    required this.title,
    required this.description,
    this.customerName,
    this.customerAddress,
    this.assignedTechnicianId,
    this.creationDate,
    this.dueDate,
    this.scheduledTime,
    this.priority = JobPriority.medium,
    this.status = JobStatus.pending,
    this.equipmentId,
    this.notes,
    this.relatedTicketId,
    this.actualCompletionDate,
    this.technicianNotes,
  });

  // For presentation purposes, a helper to get a color based on priority
  Color get priorityColor {
    switch (priority) {
      case JobPriority.critical:
        return Colors.red.shade700;
      case JobPriority.high:
        return Colors.orange.shade700;
      case JobPriority.medium:
        return Colors.amber.shade700; // Changed from yellow for better visibility
      case JobPriority.low:
        return Colors.blue.shade700;
      default:
        return Colors.grey;
    }
  }

  // Helper for status icon or color (can be expanded)
  IconData get statusIcon {
    switch (status) {
      case JobStatus.pending:
        return Icons.pending_actions_outlined;
      case JobStatus.inProgress:
        return Icons.construction_outlined;
      case JobStatus.completed:
        return Icons.check_circle_outline;
      case JobStatus.onHold:
        return Icons.pause_circle_outline_outlined;
      case JobStatus.cancelled:
        return Icons.cancel_outlined;
      case JobStatus.requiresAttention:
        return Icons.warning_amber_rounded;
      default:
        return Icons.help_outline;
    }
  }

  // Example of a copyWith method for immutability if needed later
  Job copyWith({
    String? id,
    String? title,
    String? description,
    String? customerName,
    String? customerAddress,
    String? assignedTechnicianId,
    DateTime? creationDate,
    DateTime? dueDate,
    DateTime? scheduledTime,
    JobPriority? priority,
    JobStatus? status,
    String? equipmentId,
    List<String>? notes,
    String? relatedTicketId,
    DateTime? actualCompletionDate,
    List<String>? technicianNotes,
  }) {
    return Job(
      id: id ?? this.id,
      title: title ?? this.title,
      description: description ?? this.description,
      customerName: customerName ?? this.customerName,
      customerAddress: customerAddress ?? this.customerAddress,
      assignedTechnicianId: assignedTechnicianId ?? this.assignedTechnicianId,
      creationDate: creationDate ?? this.creationDate,
      dueDate: dueDate ?? this.dueDate,
      scheduledTime: scheduledTime ?? this.scheduledTime,
      priority: priority ?? this.priority,
      status: status ?? this.status,
      equipmentId: equipmentId ?? this.equipmentId,
      notes: notes ?? this.notes,
      relatedTicketId: relatedTicketId ?? this.relatedTicketId,
      actualCompletionDate: actualCompletionDate ?? this.actualCompletionDate,
      technicianNotes: technicianNotes ?? this.technicianNotes,
    );
  }

  // JSON Serialization
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'description': description,
      'customerName': customerName,
      'customerAddress': customerAddress,
      'assignedTechnicianId': assignedTechnicianId,
      'creationDate': creationDate?.toIso8601String(),
      'dueDate': dueDate?.toIso8601String(),
      'scheduledTime': scheduledTime?.toIso8601String(),
      'priority': priority.name, // Using .name for enums (Dart 2.15+)
      'status': status.name,     // Using .name for enums
      'equipmentId': equipmentId,
      'notes': notes,
      'relatedTicketId': relatedTicketId,
      'actualCompletionDate': actualCompletionDate?.toIso8601String(),
      'technicianNotes': technicianNotes,
    };
  }

  // JSON Deserialization
  factory Job.fromJson(Map<String, dynamic> json) {
    return Job(
      id: json['id'] as String,
      title: json['title'] as String,
      description: json['description'] as String,
      customerName: json['customerName'] as String?,
      customerAddress: json['customerAddress'] as String?,
      assignedTechnicianId: json['assignedTechnicianId'] as String?,
      creationDate: json['creationDate'] == null ? null : DateTime.parse(json['creationDate'] as String),
      dueDate: json['dueDate'] == null ? null : DateTime.parse(json['dueDate'] as String),
      scheduledTime: json['scheduledTime'] == null ? null : DateTime.parse(json['scheduledTime'] as String),
      priority: JobPriority.values.firstWhere((e) => e.name == json['priority'], orElse: () => JobPriority.medium),
      status: JobStatus.values.firstWhere((e) => e.name == json['status'], orElse: () => JobStatus.pending),
      equipmentId: json['equipmentId'] as String?,
      notes: json['notes'] == null ? null : List<String>.from(json['notes'] as List),
      relatedTicketId: json['relatedTicketId'] as String?,
      actualCompletionDate: json['actualCompletionDate'] == null ? null : DateTime.parse(json['actualCompletionDate'] as String),
      technicianNotes: json['technicianNotes'] == null ? null : List<String>.from(json['technicianNotes'] as List),
    );
  }
}
