import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/job_model.dart';

// Importing the JobService
import '../services/job_service.dart';

// This is a provider for a list of jobs using JobService
final jobListProvider = StateNotifierProvider<JobListNotifier, List<Job>>((ref) {
  final jobService = JobService(baseUrl: 'https://api.example.com'); // Replace with actual base URL
  return JobListNotifier(jobService);
});

class JobListNotifier extends StateNotifier<List<Job>> {
  final JobService jobService;

  JobListNotifier(this.jobService) : super([]) {
    _fetchJobs();
  }

  Future<void> _fetchJobs() async {
    try {
      final jobs = await jobService.fetchJobs();
      state = jobs;
    } catch (e) {
      // Handle error, e.g., log or show a message
    }
  }

  // Initial mock data
  static final List<Job> _initialJobs = [
    Job(
      id: 'job-001',
      title: 'Alert: Krytyczna awaria AC-1023',
      description: 'Jednostka AC-1023 w Biurowcu Alpha przestała działać. Wymagana natychmiastowa interwencja.',
      customerName: 'Biurowiec Alpha',
      customerAddress: 'ul. Słoneczna 5, Kraków',
      priority: JobPriority.critical,
      status: JobStatus.requiresAttention,
      creationDate: DateTime.now().subtract(const Duration(hours: 1)),
      equipmentId: 'eq-001',
    ),
    Job(
      id: 'job-002',
      title: 'Przegląd okresowy HVAC-001',
      description: 'Standardowy przegląd jednostki HVAC-001 u klienta indywidualnego.',
      customerName: 'Jan Kowalski',
      customerAddress: 'ul. Leśna 12, Warszawa',
      priority: JobPriority.medium,
      status: JobStatus.pending,
      scheduledTime: DateTime.now().add(const Duration(hours: 2)),
      creationDate: DateTime.now().subtract(const Duration(days: 1)),
      equipmentId: 'eq-002',
    ),
    Job(
      id: 'job-003',
      title: 'Naprawa pompy ciepła XYZ',
      description: 'Zgłoszenie usterki pompy ciepła XYZ w firmie Delta. Problem z grzaniem.',
      customerName: 'Firma Delta',
      customerAddress: 'ul. Przemysłowa 1, Gdańsk',
      priority: JobPriority.high,
      status: JobStatus.pending,
      scheduledTime: DateTime.now().add(const Duration(hours: 5)),
      creationDate: DateTime.now().subtract(const Duration(hours: 3)),
      equipmentId: 'eq-003',
    ),
  ];

  // Method to add a new job
  void addJob(Job job) {
    state = [...state, job];
  }

  // Method to update an existing job
  void updateJob(Job updatedJob) {
    state = [
      for (final job in state)
        if (job.id == updatedJob.id) updatedJob else job,
    ];
  }

  // Method to remove a job
  void removeJob(String jobId) {
    state = state.where((job) => job.id != jobId).toList();
  }
}
