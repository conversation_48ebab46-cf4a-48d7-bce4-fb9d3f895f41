import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/equipment_status_model.dart';
import '../services/equipment_service.dart';

// Provider for the EquipmentService itself
final equipmentServiceProvider = Provider<EquipmentService>((ref) {
  // Use the GoBackend-Kratos API endpoint
  return EquipmentService(baseUrl: 'http://localhost:8080/api/v1');
});

// StateNotifierProvider for the list of equipment statuses with async state, supporting filters
final equipmentListProvider = StateNotifierProvider.family<EquipmentListNotifier, AsyncValue<List<EquipmentStatus>>, EquipmentFilter>((ref, filter) {
  final equipmentService = ref.watch(equipmentServiceProvider);
  return EquipmentListNotifier(equipmentService, filter);
});

// Provider for a single equipment status by ID
final equipmentByIdProvider = FutureProvider.family<EquipmentStatus, String>((ref, equipmentId) async {
  final equipmentService = ref.watch(equipmentServiceProvider);
  return equipmentService.fetchEquipmentById(equipmentId);
});

class EquipmentFilter {
  final String? customerId;
  final OperationalStatus? status;
  final int page;
  final int limit;

  EquipmentFilter({
    this.customerId,
    this.status,
    this.page = 1,
    this.limit = 20,
  });

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is EquipmentFilter &&
        other.customerId == customerId &&
        other.status == status &&
        other.page == page &&
        other.limit == limit;
  }

  @override
  int get hashCode => Object.hash(customerId, status, page, limit);
}

class EquipmentListNotifier extends StateNotifier<AsyncValue<List<EquipmentStatus>>> {
  final EquipmentService _equipmentService;
  final EquipmentFilter _filter;

  EquipmentListNotifier(this._equipmentService, this._filter) : super(const AsyncValue.loading()) {
    fetchEquipmentStatuses();
  }

  // Fetch equipment statuses from the service with filters
  Future<void> fetchEquipmentStatuses() async {
    state = const AsyncValue.loading();
    try {
      final equipment = await _equipmentService.fetchEquipmentStatuses(
        customerId: _filter.customerId,
        status: _filter.status,
        page: _filter.page,
        limit: _filter.limit,
      );
      state = AsyncValue.data(equipment);
    } catch (e, stackTrace) {
      state = AsyncValue.error(e, stackTrace);
    }
  }

  // Add a new equipment status
  Future<void> addEquipment(EquipmentStatus equipment) async {
    state.whenData((equipmentList) async {
      // Optimistic update
      state = AsyncValue.data([...equipmentList, equipment]);
      try {
        final addedEquipment = await _equipmentService.addEquipmentStatus(equipment);
        // Replace with server response or refetch
        await fetchEquipmentStatuses();
      } catch (e, stackTrace) {
        // Revert on error
        state = AsyncValue.data(equipmentList);
        state = AsyncValue.error(e, stackTrace);
      }
    });
  }

  // Update an existing equipment status
  Future<void> updateEquipment(EquipmentStatus updatedEquipment) async {
    state.whenData((equipmentList) async {
      // Optimistic update
      final updatedList = equipmentList.map(
        (e) => e.id == updatedEquipment.id ? updatedEquipment : e
      ).toList();
      state = AsyncValue.data(updatedList);
      
      try {
        await _equipmentService.updateEquipmentStatus(updatedEquipment);
        // Optionally refetch to ensure consistency
      } catch (e, stackTrace) {
        // Revert on error
        state = AsyncValue.data(equipmentList);
        state = AsyncValue.error(e, stackTrace);
      }
    });
  }

  // Remove an equipment status
  Future<void> removeEquipment(String equipmentId) async {
    state.whenData((equipmentList) async {
      // Optimistic update
      final filteredList = equipmentList.where((e) => e.id != equipmentId).toList();
      state = AsyncValue.data(filteredList);
      
      try {
        await _equipmentService.deleteEquipmentStatus(equipmentId);
      } catch (e, stackTrace) {
        // Revert on error
        state = AsyncValue.data(equipmentList);
        state = AsyncValue.error(e, stackTrace);
      }
    });
  }
}
