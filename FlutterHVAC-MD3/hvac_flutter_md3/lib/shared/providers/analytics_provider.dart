import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/analytics_model.dart';
import '../services/analytics_service.dart';

// Provider for the AnalyticsService itself
final analyticsServiceProvider = Provider<AnalyticsService>((ref) {
  // Use the GoBackend-Kratos API endpoint for analytics
  return AnalyticsService(baseUrl: 'http://localhost:8080/api/v1');
});

// StateNotifierProvider for the list of analytics data with async state
final analyticsDataProvider = StateNotifierProvider<AnalyticsDataNotifier, AsyncValue<List<AnalyticsData>>>((ref) {
  final analyticsService = ref.watch(analyticsServiceProvider);
  return AnalyticsDataNotifier(analyticsService);
});

class AnalyticsDataNotifier extends StateNotifier<AsyncValue<List<AnalyticsData>>> {
  final AnalyticsService _analyticsService;

  AnalyticsDataNotifier(this._analyticsService) : super(const AsyncValue.loading()) {
    fetchAnalyticsData();
  }

  // Fetch all analytics data from the service
  Future<void> fetchAnalyticsData() async {
    state = const AsyncValue.loading();
    try {
      // Fetch different types of analytics data
      final salesData = await _analyticsService.fetchSalesAnalytics();
      final customerGrowthData = await _analyticsService.fetchCustomerGrowthAnalytics();
      
      // Combine all fetched data into a single list
      final allAnalyticsData = [...salesData, ...customerGrowthData];
      
      state = AsyncValue.data(allAnalyticsData);
    } catch (e, stackTrace) {
      state = AsyncValue.error(e, stackTrace);
    }
  }

  // Method to add new analytics data (if needed for real-time updates or user-generated analytics)
  Future<void> addAnalyticsData(AnalyticsData data) async {
    state.whenData((analyticsList) {
      state = AsyncValue.data([...analyticsList, data]);
    });
    // In a real scenario, you might also call a service method to persist this data
  }

  // Method to update existing analytics data
  Future<void> updateAnalyticsData(AnalyticsData updatedData) async {
    state.whenData((analyticsList) {
      state = AsyncValue.data([
        for (final data in analyticsList)
          if (data.metricName == updatedData.metricName) updatedData else data,
      ]);
    });
    // In a real scenario, you might also call a service method to persist this data
  }

  // Method to remove analytics data
  Future<void> removeAnalyticsData(String metricName) async {
    state.whenData((analyticsList) {
      state = AsyncValue.data(analyticsList.where((data) => data.metricName != metricName).toList());
    });
    // In a real scenario, you might also call a service method to persist this data
  }
}
