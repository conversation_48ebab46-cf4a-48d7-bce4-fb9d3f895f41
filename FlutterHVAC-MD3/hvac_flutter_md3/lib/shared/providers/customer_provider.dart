import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/customer_model.dart';
import '../services/customer_service.dart';

// Provider for the CustomerService itself
final customerServiceProvider = Provider<CustomerService>((ref) {
  // Use the GoBackend-Kratos API endpoint
  return CustomerService(baseUrl: 'http://localhost:8080/api/v1');
});

// StateNotifierProvider for the list of customers with async state, supporting filters
final customerListProvider = StateNotifierProvider.family<CustomerListNotifier, AsyncValue<List<Customer>>, CustomerFilter>((ref, filter) {
  final customerService = ref.watch(customerServiceProvider);
  return CustomerListNotifier(customerService, filter);
});

// Provider for a single customer by ID
final customerByIdProvider = FutureProvider.family<Customer, String>((ref, customerId) async {
  final customerService = ref.watch(customerServiceProvider);
  return customerService.fetchCustomerById(customerId);
});

class CustomerFilter {
  final String? query;
  final String? type;
  final String? status;
  final int page;
  final int limit;

  CustomerFilter({
    this.query,
    this.type,
    this.status,
    this.page = 1,
    this.limit = 20,
  });

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is CustomerFilter &&
        other.query == query &&
        other.type == type &&
        other.status == status &&
        other.page == page &&
        other.limit == limit;
  }

  @override
  int get hashCode => Object.hash(query, type, status, page, limit);
}

class CustomerListNotifier extends StateNotifier<AsyncValue<List<Customer>>> {
  final CustomerService _customerService;
  final CustomerFilter _filter;

  CustomerListNotifier(this._customerService, this._filter) : super(const AsyncValue.loading()) {
    fetchCustomers();
  }

  // Fetch customers from the service with filters
  Future<void> fetchCustomers() async {
    state = const AsyncValue.loading();
    try {
      final customers = await _customerService.fetchCustomers(
        query: _filter.query,
        type: _filter.type,
        status: _filter.status,
        page: _filter.page,
        limit: _filter.limit,
      );
      state = AsyncValue.data(customers);
    } catch (e, stackTrace) {
      state = AsyncValue.error(e, stackTrace);
    }
  }

  // Add a new customer
  Future<void> addCustomer(Customer customer) async {
    state.whenData((customers) async {
      // Optimistic update
      state = AsyncValue.data([...customers, customer]);
      try {
        final addedCustomer = await _customerService.addCustomer(customer);
        // Replace with server response or refetch
        await fetchCustomers();
      } catch (e, stackTrace) {
        // Revert on error
        state = AsyncValue.data(customers);
        state = AsyncValue.error(e, stackTrace);
      }
    });
  }

  // Update an existing customer
  Future<void> updateCustomer(Customer updatedCustomer) async {
    state.whenData((customers) async {
      // Optimistic update
      final updatedList = customers.map(
        (c) => c.id == updatedCustomer.id ? updatedCustomer : c
      ).toList();
      state = AsyncValue.data(updatedList);
      
      try {
        await _customerService.updateCustomer(updatedCustomer);
        // Optionally refetch to ensure consistency
      } catch (e, stackTrace) {
        // Revert on error
        state = AsyncValue.data(customers);
        state = AsyncValue.error(e, stackTrace);
      }
    });
  }

  // Remove a customer
  Future<void> removeCustomer(String customerId) async {
    state.whenData((customers) async {
      // Optimistic update
      final filteredList = customers.where((c) => c.id != customerId).toList();
      state = AsyncValue.data(filteredList);
      
      try {
        await _customerService.deleteCustomer(customerId);
      } catch (e, stackTrace) {
        // Revert on error
        state = AsyncValue.data(customers);
        state = AsyncValue.error(e, stackTrace);
      }
    });
  }
}
