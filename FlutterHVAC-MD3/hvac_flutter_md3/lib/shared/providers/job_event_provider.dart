import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/job_event_model.dart';

// This is a simple provider for a list of job events
final jobEventProvider = StateNotifierProvider<JobEventNotifier, List<JobEvent>>((ref) {
  return JobEventNotifier();
});

class JobEventNotifier extends StateNotifier<List<JobEvent>> {
  JobEventNotifier() : super(_initialJobEvents);

  // Initial mock data
  static final List<JobEvent> _initialJobEvents = [
    JobEvent(
      id: 'event-001',
      title: 'HVAC Maintenance',
      date: DateTime.now().add(const Duration(days: 1)),
      description: 'Routine maintenance of HVAC system.',
    ),
    JobEvent(
      id: 'event-002',
      title: 'Emergency Repair',
      date: DateTime.now().add(const Duration(days: 2)),
      description: 'Urgent repair needed for cooling unit.',
    ),
  ];

  // Method to add a new job event
  void addJobEvent(JobEvent event) {
    state = [...state, event];
  }

  // Method to update an existing job event
  void updateJobEvent(JobEvent updatedEvent) {
    state = [
      for (final event in state)
        if (event.id == updatedEvent.id) updatedEvent else event,
    ];
  }

  // Method to remove a job event
  void removeJobEvent(String eventId) {
    state = state.where((event) => event.id != eventId).toList();
  }
}
