{"inputs": ["/home/<USER>/snap/flutter/common/flutter/packages/flutter_tools/lib/src/build_system/targets/linux.dart", "/home/<USER>/snap/flutter/common/flutter/bin/cache/artifacts/engine/linux-x64/libflutter_linux_gtk.so", "/home/<USER>/snap/flutter/common/flutter/bin/cache/artifacts/engine/linux-x64/icudtl.dat", "/home/<USER>/snap/flutter/common/flutter/bin/cache/artifacts/engine/linux-x64/flutter_linux/fl_standard_message_codec.h", "/home/<USER>/snap/flutter/common/flutter/bin/cache/artifacts/engine/linux-x64/flutter_linux/fl_binary_codec.h", "/home/<USER>/snap/flutter/common/flutter/bin/cache/artifacts/engine/linux-x64/flutter_linux/fl_method_call.h", "/home/<USER>/snap/flutter/common/flutter/bin/cache/artifacts/engine/linux-x64/flutter_linux/fl_standard_method_codec.h", "/home/<USER>/snap/flutter/common/flutter/bin/cache/artifacts/engine/linux-x64/flutter_linux/fl_dart_project.h", "/home/<USER>/snap/flutter/common/flutter/bin/cache/artifacts/engine/linux-x64/flutter_linux/fl_application.h", "/home/<USER>/snap/flutter/common/flutter/bin/cache/artifacts/engine/linux-x64/flutter_linux/fl_event_channel.h", "/home/<USER>/snap/flutter/common/flutter/bin/cache/artifacts/engine/linux-x64/flutter_linux/fl_view.h", "/home/<USER>/snap/flutter/common/flutter/bin/cache/artifacts/engine/linux-x64/flutter_linux/fl_plugin_registrar.h", "/home/<USER>/snap/flutter/common/flutter/bin/cache/artifacts/engine/linux-x64/flutter_linux/fl_method_response.h", "/home/<USER>/snap/flutter/common/flutter/bin/cache/artifacts/engine/linux-x64/flutter_linux/fl_json_message_codec.h", "/home/<USER>/snap/flutter/common/flutter/bin/cache/artifacts/engine/linux-x64/flutter_linux/fl_texture_gl.h", "/home/<USER>/snap/flutter/common/flutter/bin/cache/artifacts/engine/linux-x64/flutter_linux/fl_message_codec.h", "/home/<USER>/snap/flutter/common/flutter/bin/cache/artifacts/engine/linux-x64/flutter_linux/fl_engine.h", "/home/<USER>/snap/flutter/common/flutter/bin/cache/artifacts/engine/linux-x64/flutter_linux/fl_binary_messenger.h", "/home/<USER>/snap/flutter/common/flutter/bin/cache/artifacts/engine/linux-x64/flutter_linux/fl_texture.h", "/home/<USER>/snap/flutter/common/flutter/bin/cache/artifacts/engine/linux-x64/flutter_linux/fl_method_channel.h", "/home/<USER>/snap/flutter/common/flutter/bin/cache/artifacts/engine/linux-x64/flutter_linux/fl_plugin_registry.h", "/home/<USER>/snap/flutter/common/flutter/bin/cache/artifacts/engine/linux-x64/flutter_linux/fl_pixel_buffer_texture.h", "/home/<USER>/snap/flutter/common/flutter/bin/cache/artifacts/engine/linux-x64/flutter_linux/fl_method_codec.h", "/home/<USER>/snap/flutter/common/flutter/bin/cache/artifacts/engine/linux-x64/flutter_linux/fl_string_codec.h", "/home/<USER>/snap/flutter/common/flutter/bin/cache/artifacts/engine/linux-x64/flutter_linux/fl_texture_registrar.h", "/home/<USER>/snap/flutter/common/flutter/bin/cache/artifacts/engine/linux-x64/flutter_linux/fl_basic_message_channel.h", "/home/<USER>/snap/flutter/common/flutter/bin/cache/artifacts/engine/linux-x64/flutter_linux/flutter_linux.h", "/home/<USER>/snap/flutter/common/flutter/bin/cache/artifacts/engine/linux-x64/flutter_linux/fl_json_method_codec.h", "/home/<USER>/snap/flutter/common/flutter/bin/cache/artifacts/engine/linux-x64/flutter_linux/fl_value.h"], "outputs": ["/home/<USER>/HVAC/FlutterHVAC-MD3/hvac_flutter_md3/linux/flutter/ephemeral/libflutter_linux_gtk.so", "/home/<USER>/HVAC/FlutterHVAC-MD3/hvac_flutter_md3/linux/flutter/ephemeral/icudtl.dat", "/home/<USER>/HVAC/FlutterHVAC-MD3/hvac_flutter_md3/linux/flutter/ephemeral/flutter_linux/fl_standard_message_codec.h", "/home/<USER>/HVAC/FlutterHVAC-MD3/hvac_flutter_md3/linux/flutter/ephemeral/flutter_linux/fl_binary_codec.h", "/home/<USER>/HVAC/FlutterHVAC-MD3/hvac_flutter_md3/linux/flutter/ephemeral/flutter_linux/fl_method_call.h", "/home/<USER>/HVAC/FlutterHVAC-MD3/hvac_flutter_md3/linux/flutter/ephemeral/flutter_linux/fl_standard_method_codec.h", "/home/<USER>/HVAC/FlutterHVAC-MD3/hvac_flutter_md3/linux/flutter/ephemeral/flutter_linux/fl_dart_project.h", "/home/<USER>/HVAC/FlutterHVAC-MD3/hvac_flutter_md3/linux/flutter/ephemeral/flutter_linux/fl_application.h", "/home/<USER>/HVAC/FlutterHVAC-MD3/hvac_flutter_md3/linux/flutter/ephemeral/flutter_linux/fl_event_channel.h", "/home/<USER>/HVAC/FlutterHVAC-MD3/hvac_flutter_md3/linux/flutter/ephemeral/flutter_linux/fl_view.h", "/home/<USER>/HVAC/FlutterHVAC-MD3/hvac_flutter_md3/linux/flutter/ephemeral/flutter_linux/fl_plugin_registrar.h", "/home/<USER>/HVAC/FlutterHVAC-MD3/hvac_flutter_md3/linux/flutter/ephemeral/flutter_linux/fl_method_response.h", "/home/<USER>/HVAC/FlutterHVAC-MD3/hvac_flutter_md3/linux/flutter/ephemeral/flutter_linux/fl_json_message_codec.h", "/home/<USER>/HVAC/FlutterHVAC-MD3/hvac_flutter_md3/linux/flutter/ephemeral/flutter_linux/fl_texture_gl.h", "/home/<USER>/HVAC/FlutterHVAC-MD3/hvac_flutter_md3/linux/flutter/ephemeral/flutter_linux/fl_message_codec.h", "/home/<USER>/HVAC/FlutterHVAC-MD3/hvac_flutter_md3/linux/flutter/ephemeral/flutter_linux/fl_engine.h", "/home/<USER>/HVAC/FlutterHVAC-MD3/hvac_flutter_md3/linux/flutter/ephemeral/flutter_linux/fl_binary_messenger.h", "/home/<USER>/HVAC/FlutterHVAC-MD3/hvac_flutter_md3/linux/flutter/ephemeral/flutter_linux/fl_texture.h", "/home/<USER>/HVAC/FlutterHVAC-MD3/hvac_flutter_md3/linux/flutter/ephemeral/flutter_linux/fl_method_channel.h", "/home/<USER>/HVAC/FlutterHVAC-MD3/hvac_flutter_md3/linux/flutter/ephemeral/flutter_linux/fl_plugin_registry.h", "/home/<USER>/HVAC/FlutterHVAC-MD3/hvac_flutter_md3/linux/flutter/ephemeral/flutter_linux/fl_pixel_buffer_texture.h", "/home/<USER>/HVAC/FlutterHVAC-MD3/hvac_flutter_md3/linux/flutter/ephemeral/flutter_linux/fl_method_codec.h", "/home/<USER>/HVAC/FlutterHVAC-MD3/hvac_flutter_md3/linux/flutter/ephemeral/flutter_linux/fl_string_codec.h", "/home/<USER>/HVAC/FlutterHVAC-MD3/hvac_flutter_md3/linux/flutter/ephemeral/flutter_linux/fl_texture_registrar.h", "/home/<USER>/HVAC/FlutterHVAC-MD3/hvac_flutter_md3/linux/flutter/ephemeral/flutter_linux/fl_basic_message_channel.h", "/home/<USER>/HVAC/FlutterHVAC-MD3/hvac_flutter_md3/linux/flutter/ephemeral/flutter_linux/flutter_linux.h", "/home/<USER>/HVAC/FlutterHVAC-MD3/hvac_flutter_md3/linux/flutter/ephemeral/flutter_linux/fl_json_method_codec.h", "/home/<USER>/HVAC/FlutterHVAC-MD3/hvac_flutter_md3/linux/flutter/ephemeral/flutter_linux/fl_value.h"]}