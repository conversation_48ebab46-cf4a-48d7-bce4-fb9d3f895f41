// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

Document _$DocumentFromJson(Map<String, dynamic> json) => Document(
      id: json['id'] as String,
      title: json['title'] as String,
      description: json['description'] as String?,
      fileName: json['fileName'] as String,
      fileUrl: json['fileUrl'] as String,
      fileType: json['fileType'] as String,
      fileSize: (json['fileSize'] as num).toInt(),
      category: $enumDecode(_$DocumentCategoryEnumMap, json['category']),
      status: $enumDecode(_$DocumentStatusEnumMap, json['status']),
      uploadedById: json['uploadedById'] as String,
      uploadedByName: json['uploadedByName'] as String?,
      tags: (json['tags'] as List<dynamic>).map((e) => e as String).toList(),
      sharedWithIds: (json['sharedWithIds'] as List<dynamic>)
          .map((e) => e as String)
          .toList(),
      expiryDate: json['expiryDate'] == null
          ? null
          : DateTime.parse(json['expiryDate'] as String),
      isConfidential: json['isConfidential'] as bool,
      version: (json['version'] as num).toInt(),
      parentDocumentId: json['parentDocumentId'] as String?,
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: DateTime.parse(json['updatedAt'] as String),
    );

Map<String, dynamic> _$DocumentToJson(Document instance) => <String, dynamic>{
      'id': instance.id,
      'title': instance.title,
      'description': instance.description,
      'fileName': instance.fileName,
      'fileUrl': instance.fileUrl,
      'fileType': instance.fileType,
      'fileSize': instance.fileSize,
      'category': _$DocumentCategoryEnumMap[instance.category]!,
      'status': _$DocumentStatusEnumMap[instance.status]!,
      'uploadedById': instance.uploadedById,
      'uploadedByName': instance.uploadedByName,
      'tags': instance.tags,
      'sharedWithIds': instance.sharedWithIds,
      'expiryDate': instance.expiryDate?.toIso8601String(),
      'isConfidential': instance.isConfidential,
      'version': instance.version,
      'parentDocumentId': instance.parentDocumentId,
      'createdAt': instance.createdAt.toIso8601String(),
      'updatedAt': instance.updatedAt.toIso8601String(),
    };

const _$DocumentCategoryEnumMap = {
  DocumentCategory.contract: 'contract',
  DocumentCategory.invoice: 'invoice',
  DocumentCategory.report: 'report',
  DocumentCategory.certification: 'certification',
  DocumentCategory.policy: 'policy',
  DocumentCategory.manual: 'manual',
  DocumentCategory.form: 'form',
  DocumentCategory.other: 'other',
};

const _$DocumentStatusEnumMap = {
  DocumentStatus.draft: 'draft',
  DocumentStatus.active: 'active',
  DocumentStatus.archived: 'archived',
  DocumentStatus.expired: 'expired',
};
