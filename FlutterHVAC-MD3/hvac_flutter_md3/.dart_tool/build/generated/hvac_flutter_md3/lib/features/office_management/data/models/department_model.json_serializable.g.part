// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

Department _$DepartmentFromJson(Map<String, dynamic> json) => Department(
      id: json['id'] as String,
      name: json['name'] as String,
      description: json['description'] as String,
      managerId: json['managerId'] as String?,
      managerName: json['managerName'] as String?,
      employeeCount: (json['employeeCount'] as num).toInt(),
      employeeIds: (json['employeeIds'] as List<dynamic>)
          .map((e) => e as String)
          .toList(),
      type: $enumDecode(_$DepartmentTypeEnumMap, json['type']),
      location: json['location'] as String?,
      budget: (json['budget'] as num?)?.toDouble(),
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: DateTime.parse(json['updatedAt'] as String),
    );

Map<String, dynamic> _$DepartmentToJson(Department instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'description': instance.description,
      'managerId': instance.managerId,
      'managerName': instance.managerName,
      'employeeCount': instance.employeeCount,
      'employeeIds': instance.employeeIds,
      'type': _$DepartmentTypeEnumMap[instance.type]!,
      'location': instance.location,
      'budget': instance.budget,
      'createdAt': instance.createdAt.toIso8601String(),
      'updatedAt': instance.updatedAt.toIso8601String(),
    };

const _$DepartmentTypeEnumMap = {
  DepartmentType.operations: 'operations',
  DepartmentType.sales: 'sales',
  DepartmentType.customerService: 'customer_service',
  DepartmentType.administration: 'administration',
  DepartmentType.technical: 'technical',
  DepartmentType.management: 'management',
};
