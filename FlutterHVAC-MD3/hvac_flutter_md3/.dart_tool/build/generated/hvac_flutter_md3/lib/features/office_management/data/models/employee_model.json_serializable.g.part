// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

Employee _$EmployeeFromJson(Map<String, dynamic> json) => Employee(
      id: json['id'] as String,
      firstName: json['firstName'] as String,
      lastName: json['lastName'] as String,
      email: json['email'] as String,
      phone: json['phone'] as String,
      avatar: json['avatar'] as String?,
      department: json['department'] as String,
      position: json['position'] as String,
      role: $enumDecode(_$EmployeeRoleEnumMap, json['role']),
      status: $enumDecode(_$EmployeeStatusEnumMap, json['status']),
      hireDate: DateTime.parse(json['hireDate'] as String),
      address: json['address'] as String?,
      hourlyRate: (json['hourlyRate'] as num?)?.toDouble(),
      skills:
          (json['skills'] as List<dynamic>).map((e) => e as String).toList(),
      emergencyContact: json['emergencyContact'] as String?,
      emergencyPhone: json['emergencyPhone'] as String?,
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: DateTime.parse(json['updatedAt'] as String),
    );

Map<String, dynamic> _$EmployeeToJson(Employee instance) => <String, dynamic>{
      'id': instance.id,
      'firstName': instance.firstName,
      'lastName': instance.lastName,
      'email': instance.email,
      'phone': instance.phone,
      'avatar': instance.avatar,
      'department': instance.department,
      'position': instance.position,
      'role': _$EmployeeRoleEnumMap[instance.role]!,
      'status': _$EmployeeStatusEnumMap[instance.status]!,
      'hireDate': instance.hireDate.toIso8601String(),
      'address': instance.address,
      'hourlyRate': instance.hourlyRate,
      'skills': instance.skills,
      'emergencyContact': instance.emergencyContact,
      'emergencyPhone': instance.emergencyPhone,
      'createdAt': instance.createdAt.toIso8601String(),
      'updatedAt': instance.updatedAt.toIso8601String(),
    };

const _$EmployeeRoleEnumMap = {
  EmployeeRole.admin: 'admin',
  EmployeeRole.manager: 'manager',
  EmployeeRole.technician: 'technician',
  EmployeeRole.officeStaff: 'office_staff',
  EmployeeRole.sales: 'sales',
  EmployeeRole.customerService: 'customer_service',
};

const _$EmployeeStatusEnumMap = {
  EmployeeStatus.active: 'active',
  EmployeeStatus.inactive: 'inactive',
  EmployeeStatus.onLeave: 'on_leave',
  EmployeeStatus.terminated: 'terminated',
};
