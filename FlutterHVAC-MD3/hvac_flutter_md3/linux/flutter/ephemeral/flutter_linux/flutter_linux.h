// Copyright 2013 The Flutter Authors. All rights reserved.
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.

#ifndef FLUTTER_SHELL_PLATFORM_LINUX_PUBLIC_FLUTTER_LINUX_FLUTTER_LINUX_H_
#define FLUTTER_SHELL_PLATFORM_LINUX_PUBLIC_FLUTTER_LINUX_FLUTTER_LINUX_H_

#define __FLUTTER_LINUX_INSIDE__

#include <flutter_linux/fl_application.h>
#include <flutter_linux/fl_basic_message_channel.h>
#include <flutter_linux/fl_binary_codec.h>
#include <flutter_linux/fl_binary_messenger.h>
#include <flutter_linux/fl_dart_project.h>
#include <flutter_linux/fl_engine.h>
#include <flutter_linux/fl_event_channel.h>
#include <flutter_linux/fl_json_message_codec.h>
#include <flutter_linux/fl_json_method_codec.h>
#include <flutter_linux/fl_message_codec.h>
#include <flutter_linux/fl_method_call.h>
#include <flutter_linux/fl_method_channel.h>
#include <flutter_linux/fl_method_codec.h>
#include <flutter_linux/fl_method_response.h>
#include <flutter_linux/fl_pixel_buffer_texture.h>
#include <flutter_linux/fl_plugin_registrar.h>
#include <flutter_linux/fl_plugin_registry.h>
#include <flutter_linux/fl_standard_message_codec.h>
#include <flutter_linux/fl_standard_method_codec.h>
#include <flutter_linux/fl_string_codec.h>
#include <flutter_linux/fl_texture.h>
#include <flutter_linux/fl_texture_gl.h>
#include <flutter_linux/fl_texture_registrar.h>
#include <flutter_linux/fl_value.h>
#include <flutter_linux/fl_view.h>

#undef __FLUTTER_LINUX_INSIDE__

#endif  // FLUTTER_SHELL_PLATFORM_LINUX_PUBLIC_FLUTTER_LINUX_FLUTTER_LINUX_H_
