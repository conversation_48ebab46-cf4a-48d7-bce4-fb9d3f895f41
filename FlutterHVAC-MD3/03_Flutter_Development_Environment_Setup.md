# Flutter Development Environment Setup Guide 🛠️
## Complete Setup for HVAC CRM Flutter Application with Material Design 3

### Prerequisites Checklist ✅

Before starting, ensure you have:
- [ ] Flutter SDK 3.24.0 or later
- [ ] Dart SDK 3.7.0 or later  
- [ ] Android Studio / VS Code with Flutter extensions
- [ ] Git for version control
- [ ] Docker for backend services

## 🚀 Quick Start Commands

### 1. Create New Flutter Project
```bash
# Create new Flutter project with Material 3
flutter create --org com.hvac.crm hvac_flutter_md3
cd hvac_flutter_md3

# Enable web and desktop support
flutter config --enable-web
flutter config --enable-macos-desktop
flutter config --enable-windows-desktop
flutter config --enable-linux-desktop
```

### 2. Essential Dependencies Setup

#### Core Dependencies (pubspec.yaml)
```yaml
name: hvac_flutter_md3
description: "HVAC CRM Flutter Application with Material Design 3"
publish_to: 'none'
version: 1.0.0+1

environment:
  sdk: '>=3.7.0 <4.0.0'
  flutter: ">=3.24.0"

dependencies:
  flutter:
    sdk: flutter
  
  # Material Design 3 & UI
  material_color_utilities: ^0.11.1
  dynamic_color: ^1.7.0
  
  # State Management
  flutter_riverpod: ^2.4.9
  riverpod_annotation: ^2.3.3
  
  # Navigation
  go_router: ^14.2.0
  
  # Network & API
  grpc: ^3.2.4
  protobuf: ^3.1.0
  http: ^1.1.0
  dio: ^5.4.0
  
  # Local Storage
  hive: ^2.2.3
  hive_flutter: ^1.1.0
  flutter_secure_storage: ^9.0.0
  
  # Charts & Visualization
  fl_chart: ^0.68.0
  syncfusion_flutter_charts: ^29.1.39
  
  # Calendar & Scheduling
  table_calendar: ^3.0.9
  syncfusion_flutter_calendar: ^29.1.39
  
  # Camera & Media
  image_picker: ^1.0.4
  camera: ^0.10.5
  
  # Utilities
  intl: ^0.19.0
  uuid: ^4.2.1
  path_provider: ^2.1.1
  permission_handler: ^11.1.0
  
  # Offline & Sync
  connectivity_plus: ^5.0.2
  
  # Voice & Speech
  speech_to_text: ^6.6.0
  
  # Animations
  lottie: ^2.7.0
  
  # Icons
  cupertino_icons: ^1.0.6

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^3.0.0
  
  # Code Generation
  build_runner: ^2.4.7
  riverpod_generator: ^2.3.9
  hive_generator: ^2.0.1
  json_annotation: ^4.8.1
  json_serializable: ^6.7.1

flutter:
  uses-material-design: true
  
  assets:
    - assets/images/
    - assets/icons/
    - assets/animations/
  
  fonts:
    - family: Roboto
      fonts:
        - asset: fonts/Roboto-Regular.ttf
        - asset: fonts/Roboto-Bold.ttf
          weight: 700
```

### 3. Install Dependencies
```bash
# Get all packages
flutter pub get

# Run code generation
dart run build_runner build
```

## 🎨 Material Design 3 Theme Setup

### 1. Create Theme Configuration
```dart
// lib/core/theme/app_theme.dart
import 'package:flutter/material.dart';
import 'package:material_color_utilities/material_color_utilities.dart';
import 'package:dynamic_color/dynamic_color.dart';

class AppTheme {
  // HVAC Brand Colors
  static const Color _hvacBlue = Color(0xFF1976D2);
  static const Color _hvacOrange = Color(0xFFFF9800);
  
  static ThemeData lightTheme(ColorScheme? dynamicColorScheme) {
    final colorScheme = dynamicColorScheme ?? 
        ColorScheme.fromSeed(
          seedColor: _hvacBlue,
          brightness: Brightness.light,
        );
    
    return ThemeData(
      useMaterial3: true,
      colorScheme: colorScheme,
      
      // App Bar Theme
      appBarTheme: AppBarTheme(
        centerTitle: true,
        elevation: 0,
        backgroundColor: colorScheme.surface,
        foregroundColor: colorScheme.onSurface,
        surfaceTintColor: colorScheme.surfaceTint,
      ),
      
      // Card Theme
      cardTheme: CardTheme(
        elevation: 2,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        clipBehavior: Clip.antiAlias,
      ),
      
      // Elevated Button Theme
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          elevation: 2,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          padding: const EdgeInsets.symmetric(
            horizontal: 24,
            vertical: 12,
          ),
        ),
      ),
      
      // Filled Button Theme
      filledButtonTheme: FilledButtonThemeData(
        style: FilledButton.styleFrom(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          padding: const EdgeInsets.symmetric(
            horizontal: 24,
            vertical: 12,
          ),
        ),
      ),
      
      // Input Decoration Theme
      inputDecorationTheme: InputDecorationTheme(
        filled: true,
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide.none,
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(
            color: colorScheme.outline.withOpacity(0.5),
          ),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(
            color: colorScheme.primary,
            width: 2,
          ),
        ),
        errorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(
            color: colorScheme.error,
          ),
        ),
      ),
      
      // Navigation Bar Theme
      navigationBarTheme: NavigationBarThemeData(
        elevation: 3,
        height: 80,
        labelBehavior: NavigationDestinationLabelBehavior.alwaysShow,
      ),
      
      // Chip Theme
      chipTheme: ChipThemeData(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
        labelPadding: const EdgeInsets.symmetric(horizontal: 8),
      ),
      
      // Dialog Theme
      dialogTheme: DialogTheme(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(20),
        ),
        elevation: 6,
      ),
      
      // Bottom Sheet Theme
      bottomSheetTheme: const BottomSheetThemeData(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.vertical(
            top: Radius.circular(20),
          ),
        ),
        elevation: 8,
      ),
    );
  }
  
  static ThemeData darkTheme(ColorScheme? dynamicColorScheme) {
    final colorScheme = dynamicColorScheme ?? 
        ColorScheme.fromSeed(
          seedColor: _hvacBlue,
          brightness: Brightness.dark,
        );
    
    return lightTheme(colorScheme).copyWith(
      colorScheme: colorScheme,
    );
  }
}
```

### 2. Main App Setup with Dynamic Colors
```dart
// lib/main.dart
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:dynamic_color/dynamic_color.dart';
import 'package:hive_flutter/hive_flutter.dart';

import 'core/theme/app_theme.dart';
import 'core/router/app_router.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  
  // Initialize Hive
  await Hive.initFlutter();
  
  runApp(
    ProviderScope(
      child: const HVACApp(),
    ),
  );
}

class HVACApp extends ConsumerWidget {
  const HVACApp({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final router = ref.watch(routerProvider);
    
    return DynamicColorBuilder(
      builder: (lightDynamic, darkDynamic) {
        return MaterialApp.router(
          title: 'HVAC CRM',
          debugShowCheckedModeBanner: false,
          
          // Theme Configuration
          theme: AppTheme.lightTheme(lightDynamic),
          darkTheme: AppTheme.darkTheme(darkDynamic),
          themeMode: ThemeMode.system,
          
          // Router Configuration
          routerConfig: router,
          
          // Localization
          supportedLocales: const [
            Locale('en', 'US'),
            Locale('pl', 'PL'),
          ],
        );
      },
    );
  }
}
```

## 🗂️ Project Structure Setup

### 1. Create Folder Structure
```bash
mkdir -p lib/{core,features,shared}
mkdir -p lib/core/{theme,router,constants,utils,services}
mkdir -p lib/features/{auth,dashboard,customers,jobs,equipment,calendar,reports}
mkdir -p lib/shared/{widgets,models,providers,extensions}
mkdir -p assets/{images,icons,animations,fonts}
```

### 2. Recommended Project Structure
```
lib/
├── core/
│   ├── theme/
│   │   ├── app_theme.dart
│   │   └── color_schemes.dart
│   ├── router/
│   │   ├── app_router.dart
│   │   └── route_names.dart
│   ├── constants/
│   │   ├── app_constants.dart
│   │   └── api_endpoints.dart
│   ├── services/
│   │   ├── api_service.dart
│   │   ├── storage_service.dart
│   │   └── auth_service.dart
│   └── utils/
│       ├── extensions.dart
│       └── helpers.dart
├── features/
│   ├── auth/
│   │   ├── data/
│   │   ├── domain/
│   │   ├── presentation/
│   │   └── providers/
│   ├── dashboard/
│   ├── customers/
│   ├── jobs/
│   ├── equipment/
│   ├── calendar/
│   └── reports/
├── shared/
│   ├── widgets/
│   │   ├── buttons/
│   │   ├── cards/
│   │   ├── forms/
│   │   └── layouts/
│   ├── models/
│   ├── providers/
│   └── extensions/
└── main.dart
```

## 🔧 Essential Packages Configuration

### 1. Riverpod State Management Setup
```dart
// lib/shared/providers/app_providers.dart
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'app_providers.g.dart';

// Theme Provider
@riverpod
class ThemeMode extends _$ThemeMode {
  @override
  ThemeMode build() => ThemeMode.system;
  
  void setTheme(ThemeMode mode) {
    state = mode;
  }
}

// Connectivity Provider
@riverpod
class ConnectivityStatus extends _$ConnectivityStatus {
  @override
  bool build() => true;
  
  void updateStatus(bool isConnected) {
    state = isConnected;
  }
}
```

### 2. Go Router Setup
```dart
// lib/core/router/app_router.dart
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../features/auth/presentation/login_screen.dart';
import '../../features/dashboard/presentation/dashboard_screen.dart';
import '../../features/customers/presentation/customers_screen.dart';

final routerProvider = Provider<GoRouter>((ref) {
  return GoRouter(
    initialLocation: '/dashboard',
    routes: [
      // Auth Routes
      GoRoute(
        path: '/login',
        name: 'login',
        builder: (context, state) => const LoginScreen(),
      ),
      
      // Main App Routes
      ShellRoute(
        builder: (context, state, child) {
          return MainShell(child: child);
        },
        routes: [
          GoRoute(
            path: '/dashboard',
            name: 'dashboard',
            builder: (context, state) => const DashboardScreen(),
          ),
          GoRoute(
            path: '/customers',
            name: 'customers',
            builder: (context, state) => const CustomersScreen(),
            routes: [
              GoRoute(
                path: '/:id',
                name: 'customer-detail',
                builder: (context, state) {
                  final id = state.pathParameters['id']!;
                  return CustomerDetailScreen(customerId: id);
                },
              ),
            ],
          ),
          GoRoute(
            path: '/jobs',
            name: 'jobs',
            builder: (context, state) => const JobsScreen(),
          ),
          GoRoute(
            path: '/equipment',
            name: 'equipment',
            builder: (context, state) => const EquipmentScreen(),
          ),
          GoRoute(
            path: '/calendar',
            name: 'calendar',
            builder: (context, state) => const CalendarScreen(),
          ),
        ],
      ),
    ],
  );
});
```

### 3. API Service Setup
```dart
// lib/core/services/api_service.dart
import 'package:dio/dio.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

class ApiService {
  late final Dio _dio;
  
  ApiService() {
    _dio = Dio(BaseOptions(
      baseUrl: 'https://api.hvac-crm.com',
      connectTimeout: const Duration(seconds: 30),
      receiveTimeout: const Duration(seconds: 30),
      headers: {
        'Content-Type': 'application/json',
      },
    ));
    
    _setupInterceptors();
  }
  
  void _setupInterceptors() {
    _dio.interceptors.add(
      InterceptorsWrapper(
        onRequest: (options, handler) {
          // Add auth token
          final token = _getAuthToken();
          if (token != null) {
            options.headers['Authorization'] = 'Bearer $token';
          }
          handler.next(options);
        },
        onError: (error, handler) {
          // Handle errors globally
          _handleApiError(error);
          handler.next(error);
        },
      ),
    );
  }
  
  String? _getAuthToken() {
    // Get token from secure storage
    return null; // Implement token retrieval
  }
  
  void _handleApiError(DioException error) {
    // Global error handling
    print('API Error: ${error.message}');
  }
}

final apiServiceProvider = Provider<ApiService>((ref) => ApiService());
```

## 📱 Essential UI Components

### 1. Custom App Bar
```dart
// lib/shared/widgets/layouts/custom_app_bar.dart
import 'package:flutter/material.dart';

class CustomAppBar extends StatelessWidget implements PreferredSizeWidget {
  final String title;
  final List<Widget>? actions;
  final Widget? leading;
  final bool centerTitle;
  
  const CustomAppBar({
    super.key,
    required this.title,
    this.actions,
    this.leading,
    this.centerTitle = true,
  });

  @override
  Widget build(BuildContext context) {
    return AppBar(
      title: Text(
        title,
        style: Theme.of(context).textTheme.titleLarge?.copyWith(
          fontWeight: FontWeight.w600,
        ),
      ),
      centerTitle: centerTitle,
      leading: leading,
      actions: actions,
      elevation: 0,
      scrolledUnderElevation: 2,
    );
  }

  @override
  Size get preferredSize => const Size.fromHeight(kToolbarHeight);
}
```

### 2. Status Card Widget
```dart
// lib/shared/widgets/cards/status_card.dart
import 'package:flutter/material.dart';

class StatusCard extends StatelessWidget {
  final String title;
  final String value;
  final IconData icon;
  final Color? color;
  final VoidCallback? onTap;
  
  const StatusCard({
    super.key,
    required this.title,
    required this.value,
    required this.icon,
    this.color,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final cardColor = color ?? theme.colorScheme.primary;
    
    return Card(
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(16),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Icon(
                    icon,
                    color: cardColor,
                    size: 24,
                  ),
                  const Spacer(),
                  if (onTap != null)
                    Icon(
                      Icons.arrow_forward_ios,
                      size: 16,
                      color: theme.colorScheme.onSurfaceVariant,
                    ),
                ],
              ),
              const SizedBox(height: 12),
              Text(
                value,
                style: theme.textTheme.headlineMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: cardColor,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                title,
                style: theme.textTheme.bodyMedium?.copyWith(
                  color: theme.colorScheme.onSurfaceVariant,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
```

### 3. Loading States
```dart
// lib/shared/widgets/loading/loading_widget.dart
import 'package:flutter/material.dart';
import 'package:lottie/lottie.dart';

class LoadingWidget extends StatelessWidget {
  final String? message;
  final double size;
  
  const LoadingWidget({
    super.key,
    this.message,
    this.size = 100,
  });

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          SizedBox(
            width: size,
            height: size,
            child: CircularProgressIndicator(
              strokeWidth: 3,
              color: Theme.of(context).colorScheme.primary,
            ),
          ),
          if (message != null) ...[
            const SizedBox(height: 16),
            Text(
              message!,
              style: Theme.of(context).textTheme.bodyMedium,
              textAlign: TextAlign.center,
            ),
          ],
        ],
      ),
    );
  }
}
```

## 🔧 Development Tools Setup

### 1. VS Code Configuration
Create `.vscode/settings.json`:
```json
{
  "dart.flutterSdkPath": "/path/to/flutter",
  "dart.lineLength": 80,
  "editor.rulers": [80],
  "editor.formatOnSave": true,
  "dart.previewFlutterUiGuides": true,
  "dart.previewFlutterUiGuidesCustomTracking": true,
  "files.associations": {
    "*.dart": "dart"
  },
  "emmet.includeLanguages": {
    "dart": "html"
  }
}
```

### 2. Analysis Options
Create `analysis_options.yaml`:
```yaml
include: package:flutter_lints/flutter.yaml

analyzer:
  exclude:
    - "**/*.g.dart"
    - "**/*.freezed.dart"
  
  language:
    strict-casts: true
    strict-inference: true
    strict-raw-types: true

linter:
  rules:
    # Style rules
    prefer_single_quotes: true
    prefer_relative_imports: true
    prefer_const_constructors: true
    prefer_const_literals_to_create_immutables: true
    
    # Error prevention
    avoid_print: true
    avoid_unnecessary_containers: true
    avoid_web_libraries_in_flutter: true
    
    # Performance
    prefer_const_declarations: true
    prefer_final_fields: true
    prefer_final_locals: true
```

### 3. Git Configuration
Create `.gitignore`:
```gitignore
# Flutter/Dart
.dart_tool/
.flutter-plugins
.flutter-plugins-dependencies
.packages
.pub-cache/
.pub/
build/
flutter_*.png

# IDE
.vscode/
.idea/
*.iml
*.ipr
*.iws

# Generated files
**/*.g.dart
**/*.freezed.dart
**/*.mocks.dart

# Environment
.env
.env.local
.env.production

# Platform specific
android/app/debug
android/app/profile
android/app/release
ios/Flutter/flutter_export_environment.sh
macos/Flutter/flutter_export_environment.sh
```

## 🚀 Quick Development Commands

### Essential Flutter Commands
```bash
# Create new feature
flutter create --template=package features/new_feature

# Run app in debug mode
flutter run

# Run with hot reload
flutter run --hot

# Build for production
flutter build apk --release
flutter build ios --release

# Run tests
flutter test

# Analyze code
flutter analyze

# Format code
dart format .

# Generate code
dart run build_runner build --delete-conflicting-outputs

# Clean project
flutter clean && flutter pub get
```

### Performance & Debugging
```bash
# Run with performance overlay
flutter run --profile

# Debug with inspector
flutter run --debug

# Check app size
flutter build apk --analyze-size

# Profile memory usage
flutter run --profile --trace-startup
```

## 📋 Development Checklist

### Before Starting Development
- [ ] Flutter SDK installed and configured
- [ ] IDE with Flutter extensions setup
- [ ] Project structure created
- [ ] Dependencies added to pubspec.yaml
- [ ] Theme configuration implemented
- [ ] Router setup completed
- [ ] State management configured
- [ ] API service structure ready

### During Development
- [ ] Follow Material Design 3 guidelines
- [ ] Implement responsive design
- [ ] Add proper error handling
- [ ] Write unit tests for business logic
- [ ] Add widget tests for UI components
- [ ] Implement offline capabilities
- [ ] Add proper logging and monitoring

### Before Production
- [ ] Performance testing completed
- [ ] Security review done
- [ ] Accessibility testing passed
- [ ] Cross-platform testing finished
- [ ] App store guidelines compliance
- [ ] CI/CD pipeline configured

---

*This setup guide provides everything needed to start developing a professional HVAC CRM Flutter application with Material Design 3. Follow the steps sequentially for the best development experience.* 🚀