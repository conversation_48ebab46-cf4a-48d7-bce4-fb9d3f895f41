#!/bin/bash

# HVAC Flutter MD3 Development Environment Starter
echo "🚀 Starting HVAC Flutter MD3 Development Environment"

# Check if Docker is available
if command -v docker &> /dev/null; then
    echo "✅ Docker found - using containerized environment"
    
    # Check if docker-compose is available
    if command -v docker-compose &> /dev/null; then
        echo "🐳 Starting Flutter development container..."
        docker-compose up flutter-dev
    else
        echo "❌ docker-compose not found. Please install docker-compose."
        exit 1
    fi
else
    echo "⚠️  Docker not found - checking for local Flutter installation"
    
    # Check if Flutter is available
    if command -v flutter &> /dev/null; then
        echo "✅ Flutter found - using local installation"
        cd hvac_flutter_md3
        
        echo "📦 Installing dependencies..."
        flutter pub get
        
        echo "🔧 Running code generation..."
        dart run build_runner build
        
        echo "🌐 Starting Flutter web server..."
        flutter run -d web-server --web-port 3000 --web-hostname 0.0.0.0
    else
        echo "❌ Neither Docker nor Flutter found."
        echo ""
        echo "Please install one of the following:"
        echo "1. Docker & Docker Compose (recommended)"
        echo "2. Flutter SDK 3.24.0 or later"
        echo ""
        echo "For Flutter installation, see: 03_Flutter_Development_Environment_Setup.md"
        exit 1
    fi
fi