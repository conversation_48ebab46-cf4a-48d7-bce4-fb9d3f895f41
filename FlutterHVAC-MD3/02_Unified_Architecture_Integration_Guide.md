# Unified Architecture Integration Guide 🏗️
## HVAC-Remix + GoBackend-Kratos + Flutter MD3 Harmonious Integration

### Executive Summary
This document outlines the comprehensive integration strategy for combining HVAC-Remix (React/Remix frontend), GoBackend-Kratos (Go microservices), and Flutter with Material Design 3 into a unified, powerful HVAC CRM ecosystem.

## 🎯 Integration Architecture Overview

### Core Integration Principles
1. **Non-Locality**: Deep integration across all layers
2. **Suchness**: Intuitive user experience across platforms
3. **Adaptacyjność**: Flexible architecture supporting multiple clients
4. **Transformacyjność**: AI-powered evolution with LLM Bielik V3

## 🔗 System Architecture Layers

### 1. Backend Layer - GoBackend-<PERSON>ratos
```
┌─────────────────────────────────────────┐
│           GoBackend-Kratos              │
├─────────────────────────────────────────┤
│ • gRPC Services (HVAC, AI, Email)      │
│ • Kratos Identity Management           │
│ • PostgreSQL Database                  │
│ • Redis Caching                        │
│ • Event-driven Architecture            │
│ • Microservices Pattern                │
└─────────────────────────────────────────┘
```

**Key Services:**
- **HVACService**: Customer, Job, Device management
- **AIService**: LLM integration, analysis
- **EmailService**: Communication processing
- **AuthService**: Identity and access management

### 2. Web Frontend Layer - HVAC-Remix
```
┌─────────────────────────────────────────┐
│             HVAC-Remix                  │
├─────────────────────────────────────────┤
│ • React + Remix Framework              │
│ • Server-Side Rendering                │
│ • GraphQL + tRPC Integration           │
│ • Supabase Database                    │
│ • Progressive Web App                  │
│ • Real-time Updates                    │
└─────────────────────────────────────────┘
```

**Key Features:**
- Atomic Design UI Components
- Predictive Maintenance AI
- OCR Document Processing
- Vector Search with Qdrant
- Bielik LLM Integration

### 3. Mobile Layer - Flutter MD3
```
┌─────────────────────────────────────────┐
│          Flutter MD3 Mobile             │
├─────────────────────────────────────────┤
│ • Material Design 3 Components         │
│ • Riverpod State Management            │
│ • gRPC Client Integration              │
│ • Offline-First Architecture           │
│ • Real-time Synchronization            │
│ • Cross-platform (iOS/Android)         │
└─────────────────────────────────────────┘
```

## 🔄 Communication Protocols

### 1. GoBackend-Kratos ↔ HVAC-Remix
```typescript
// tRPC Router Integration
export const hvacRouter = router({
  // Customer Management
  customer: {
    list: publicProcedure.query(async () => {
      const response = await grpcClient.listCustomers({});
      return response.customers;
    }),
    create: publicProcedure
      .input(customerSchema)
      .mutation(async ({ input }) => {
        return await grpcClient.createCustomer(input);
      }),
  },
  
  // Job Management with Real-time
  job: {
    list: publicProcedure.query(async () => {
      const response = await grpcClient.listJobs({});
      return response.jobs;
    }),
    subscribe: publicProcedure.subscription(() => {
      return observable<Job>((emit) => {
        const stream = grpcClient.subscribeToJobs({});
        stream.on('data', (job) => emit.next(job));
        return () => stream.cancel();
      });
    }),
  },
});
```

### 2. GoBackend-Kratos ↔ Flutter MD3
```dart
// gRPC Client Integration
class HVACKratosClient {
  final HVACServiceClient _client;
  
  HVACKratosClient() : _client = HVACServiceClient(
    ClientChannel(
      'localhost',
      port: 9000,
      options: const ChannelOptions(
        credentials: ChannelCredentials.insecure(),
      ),
    ),
  );
  
  Future<List<Customer>> getCustomers() async {
    final request = ListCustomersRequest();
    final response = await _client.listCustomers(request);
    return response.customers
        .map((c) => Customer.fromProto(c))
        .toList();
  }
  
  Stream<Job> subscribeToJobs() {
    final request = SubscribeJobsRequest();
    return _client.subscribeToJobs(request)
        .map((response) => Job.fromProto(response.job));
  }
}
```

### 3. HVAC-Remix ↔ Flutter MD3 (Shared Data)
```dart
// Shared API Layer
class SharedAPIService {
  static const String baseUrl = 'https://hvac-remix.app/api';
  
  Future<List<ServiceOrder>> getServiceOrders() async {
    final response = await http.get(
      Uri.parse('$baseUrl/service-orders'),
      headers: {'Authorization': 'Bearer $token'},
    );
    
    if (response.statusCode == 200) {
      final data = json.decode(response.body);
      return data.map<ServiceOrder>((json) => 
          ServiceOrder.fromJson(json)).toList();
    }
    throw Exception('Failed to load service orders');
  }
}
```

## 🗄️ Data Synchronization Strategy

### 1. Event-Driven Architecture
```go
// GoBackend-Kratos Event Publisher
type EventPublisher struct {
    redisClient *redis.Client
}

func (ep *EventPublisher) PublishJobUpdate(job *Job) error {
    event := JobUpdateEvent{
        Type: "job.updated",
        JobID: job.ID,
        Data: job,
        Timestamp: time.Now(),
    }
    
    return ep.redisClient.Publish(
        "hvac.jobs.updates", 
        json.Marshal(event),
    ).Err()
}
```

### 2. Real-time Synchronization
```typescript
// HVAC-Remix Real-time Listener
export const jobUpdateSubscription = createTRPCMsw.subscription(
  'job.subscribe',
  () => {
    return observable<JobUpdate>((emit) => {
      const eventSource = new EventSource('/api/jobs/stream');
      
      eventSource.onmessage = (event) => {
        const jobUpdate = JSON.parse(event.data);
        emit.next(jobUpdate);
      };
      
      return () => eventSource.close();
    });
  }
);
```

### 3. Offline-First Flutter Strategy
```dart
// Flutter Offline-First with Riverpod
final jobsProvider = AsyncNotifierProvider<JobsNotifier, List<Job>>(
  JobsNotifier.new,
);

class JobsNotifier extends AsyncNotifier<List<Job>> {
  @override
  Future<List<Job>> build() async {
    // Try to load from local storage first
    final localJobs = await _loadFromLocal();
    if (localJobs.isNotEmpty) {
      // Load local data immediately
      state = AsyncValue.data(localJobs);
      // Then sync with server in background
      _syncWithServer();
      return localJobs;
    }
    
    // If no local data, fetch from server
    return await _fetchFromServer();
  }
  
  Future<void> _syncWithServer() async {
    try {
      final serverJobs = await _kratosClient.getJobs();
      await _saveToLocal(serverJobs);
      state = AsyncValue.data(serverJobs);
    } catch (e) {
      // Keep local data if sync fails
      print('Sync failed: $e');
    }
  }
}
```

## 🔐 Authentication & Authorization

### 1. Unified Identity with Kratos
```go
// GoBackend-Kratos Identity Service
type IdentityService struct {
    kratosClient *kratos.Client
}

func (is *IdentityService) ValidateSession(sessionToken string) (*Identity, error) {
    session, err := is.kratosClient.ToSession(sessionToken)
    if err != nil {
        return nil, err
    }
    
    return &Identity{
        ID: session.Identity.ID,
        Email: session.Identity.Traits["email"].(string),
        Roles: session.Identity.Traits["roles"].([]string),
    }, nil
}
```

### 2. HVAC-Remix Session Management
```typescript
// Session validation middleware
export async function validateSession(request: Request) {
  const sessionToken = request.headers.get('Authorization')?.replace('Bearer ', '');
  
  if (!sessionToken) {
    throw new Response('Unauthorized', { status: 401 });
  }
  
  const identity = await kratosClient.validateSession(sessionToken);
  return identity;
}
```

### 3. Flutter Secure Storage
```dart
// Flutter secure token management
class AuthService {
  static const _storage = FlutterSecureStorage();
  
  Future<String?> getToken() async {
    return await _storage.read(key: 'auth_token');
  }
  
  Future<void> saveToken(String token) async {
    await _storage.write(key: 'auth_token', value: token);
  }
  
  Future<bool> isAuthenticated() async {
    final token = await getToken();
    if (token == null) return false;
    
    try {
      final response = await _kratosClient.validateSession(token);
      return response.isValid;
    } catch (e) {
      return false;
    }
  }
}
```

## 🎨 UI/UX Consistency

### 1. Shared Design System
```dart
// Flutter MD3 Theme matching HVAC-Remix
class HVACTheme {
  static ThemeData get lightTheme => ThemeData(
    useMaterial3: true,
    colorScheme: ColorScheme.fromSeed(
      seedColor: const Color(0xFF1976D2), // Match HVAC-Remix blue
      brightness: Brightness.light,
    ),
    cardTheme: CardTheme(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
    ),
    appBarTheme: const AppBarTheme(
      centerTitle: true,
      elevation: 0,
    ),
  );
}
```

### 2. Component Mapping
| HVAC-Remix Component | Flutter MD3 Equivalent | Purpose |
|---------------------|------------------------|---------|
| `<Card>` | `Card` widget | Equipment status display |
| `<Button>` | `ElevatedButton` | Primary actions |
| `<Badge>` | `Chip` | Status indicators |
| `<Modal>` | `showDialog` | Forms and details |
| `<Toast>` | `SnackBar` | Notifications |

## 📱 Mobile-Specific Features

### 1. Offline Capabilities
```dart
// Offline job management
class OfflineJobService {
  final _localDB = Hive.box<Job>('jobs');
  final _syncQueue = Hive.box<SyncAction>('sync_queue');
  
  Future<void> createJobOffline(Job job) async {
    // Save locally with pending status
    job.syncStatus = SyncStatus.pending;
    await _localDB.put(job.id, job);
    
    // Queue for sync when online
    await _syncQueue.add(SyncAction(
      type: SyncActionType.create,
      entityType: 'job',
      entityId: job.id,
      data: job.toJson(),
    ));
  }
  
  Future<void> syncPendingChanges() async {
    if (!await _isOnline()) return;
    
    final pendingActions = _syncQueue.values.toList();
    for (final action in pendingActions) {
      try {
        await _executeSyncAction(action);
        await _syncQueue.delete(action.key);
      } catch (e) {
        print('Sync failed for action ${action.id}: $e');
      }
    }
  }
}
```

### 2. Camera Integration for Equipment Photos
```dart
// Equipment photo capture
class EquipmentPhotoService {
  Future<String?> captureEquipmentPhoto(String equipmentId) async {
    final ImagePicker picker = ImagePicker();
    final XFile? image = await picker.pickImage(
      source: ImageSource.camera,
      maxWidth: 1920,
      maxHeight: 1080,
      imageQuality: 85,
    );
    
    if (image != null) {
      // Compress and upload
      final compressedImage = await _compressImage(image);
      final uploadUrl = await _uploadToStorage(compressedImage, equipmentId);
      
      // Update equipment record
      await _updateEquipmentPhoto(equipmentId, uploadUrl);
      
      return uploadUrl;
    }
    return null;
  }
}
```

## 🤖 AI Integration Across Platforms

### 1. Shared AI Services
```dart
// Unified AI service for predictive maintenance
class PredictiveMaintenanceService {
  final HVACKratosClient _kratosClient;
  
  Future<MaintenancePrediction> predictMaintenance(
    String deviceId,
    Map<String, dynamic> telemetryData,
  ) async {
    final request = AnalyzeRequest()
      ..deviceId = deviceId
      ..telemetryData = jsonEncode(telemetryData)
      ..analysisType = 'predictive_maintenance';
    
    final response = await _kratosClient.analyze(request);
    
    return MaintenancePrediction.fromJson(
      jsonDecode(response.result),
    );
  }
}
```

### 2. Voice Commands (Flutter-specific)
```dart
// Voice command integration
class VoiceCommandService {
  final SpeechToText _speechToText = SpeechToText();
  
  Future<void> startListening() async {
    if (await _speechToText.initialize()) {
      _speechToText.listen(
        onResult: (result) {
          _processVoiceCommand(result.recognizedWords);
        },
      );
    }
  }
  
  void _processVoiceCommand(String command) {
    if (command.contains('schedule maintenance')) {
      // Extract equipment info and schedule
      _scheduleMaintenanceFromVoice(command);
    } else if (command.contains('show equipment status')) {
      // Navigate to equipment dashboard
      _showEquipmentStatus();
    }
  }
}
```

## 🔄 Development Workflow

### 1. Shared Development Environment
```yaml
# docker-compose.yml for unified development
version: '3.8'
services:
  gobackend-kratos:
    build: ./GoBackend-Kratos
    ports:
      - "9000:9000"  # gRPC
      - "8080:8080"  # HTTP
    environment:
      - DATABASE_URL=************************************/hvac
      - REDIS_URL=redis://redis:6379
    
  hvac-remix:
    build: ./hvac-remix
    ports:
      - "3000:3000"
    environment:
      - GRPC_ENDPOINT=gobackend-kratos:9000
      - DATABASE_URL=************************************/hvac
    
  postgres:
    image: postgres:15
    environment:
      POSTGRES_DB: hvac
      POSTGRES_USER: user
      POSTGRES_PASSWORD: pass
    
  redis:
    image: redis:7-alpine
```

### 2. Code Generation Pipeline
```bash
#!/bin/bash
# generate-protos.sh

# Generate Go code from protobuf
protoc --go_out=. --go-grpc_out=. api/hvac/v1/*.proto

# Generate TypeScript client for HVAC-Remix
protoc --plugin=protoc-gen-ts_proto=./node_modules/.bin/protoc-gen-ts_proto \
       --ts_proto_out=./hvac-remix/src/generated \
       api/hvac/v1/*.proto

# Generate Dart client for Flutter
protoc --dart_out=grpc:./flutter-hvac/lib/generated \
       api/hvac/v1/*.proto
```

## 📊 Monitoring & Observability

### 1. Unified Logging
```go
// Structured logging across services
type Logger struct {
    *logrus.Logger
}

func (l *Logger) LogJobUpdate(jobID string, userID string, action string) {
    l.WithFields(logrus.Fields{
        "service": "hvac-kratos",
        "job_id": jobID,
        "user_id": userID,
        "action": action,
        "timestamp": time.Now(),
    }).Info("Job updated")
}
```

### 2. Performance Monitoring
```dart
// Flutter performance tracking
class PerformanceMonitor {
  static void trackScreenLoad(String screenName) {
    final stopwatch = Stopwatch()..start();
    
    WidgetsBinding.instance.addPostFrameCallback((_) {
      stopwatch.stop();
      FirebasePerformance.instance
          .newTrace('screen_load_$screenName')
          .setMetric('duration_ms', stopwatch.elapsedMilliseconds)
          .stop();
    });
  }
}
```

## 🚀 Deployment Strategy

### 1. Kubernetes Deployment
```yaml
# k8s-deployment.yml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: hvac-ecosystem
spec:
  replicas: 3
  selector:
    matchLabels:
      app: hvac-ecosystem
  template:
    spec:
      containers:
      - name: gobackend-kratos
        image: hvac/gobackend-kratos:latest
        ports:
        - containerPort: 9000
        - containerPort: 8080
      - name: hvac-remix
        image: hvac/hvac-remix:latest
        ports:
        - containerPort: 3000
```

### 2. Flutter App Distribution
```yaml
# .github/workflows/flutter-deploy.yml
name: Flutter Deploy
on:
  push:
    branches: [main]
    paths: ['flutter-hvac/**']

jobs:
  build-and-deploy:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v3
    - uses: subosito/flutter-action@v2
      with:
        flutter-version: '3.24.0'
    
    - name: Build APK
      run: |
        cd flutter-hvac
        flutter build apk --release
    
    - name: Deploy to Firebase App Distribution
      uses: wzieba/Firebase-Distribution-Github-Action@v1
      with:
        appId: ${{ secrets.FIREBASE_APP_ID }}
        token: ${{ secrets.FIREBASE_TOKEN }}
        groups: testers
        file: flutter-hvac/build/app/outputs/flutter-apk/app-release.apk
```

## 🎯 Next Steps

1. **Phase 1**: Establish gRPC communication between all services
2. **Phase 2**: Implement shared authentication with Kratos
3. **Phase 3**: Build Flutter MD3 UI components
4. **Phase 4**: Add offline capabilities and synchronization
5. **Phase 5**: Integrate AI services across platforms
6. **Phase 6**: Performance optimization and monitoring

---

*This integration guide provides the foundation for a truly unified HVAC CRM ecosystem that leverages the strengths of each platform while maintaining consistency and performance across all touchpoints.* 🚀