# Flutter HVAC CRM with Material Design 3 🚀

## Comprehensive HVAC CRM Solution

This project provides a complete Flutter application for HVAC CRM with Material Design 3, integrated with HVAC-Remix and GoBackend-Kratos.

## 📁 Project Structure

```
FlutterHVAC-MD3/
├── 01_Material_Design_3_Expressive_Integration.md
├── 02_Unified_Architecture_Integration_Guide.md
├── 03_Flutter_Development_Environment_Setup.md
├── 04_Best_Flutter_Packages_Guide.md
├── hvac_flutter_md3/                 # Flutter application
│   ├── lib/
│   │   ├── core/                     # Core functionality
│   │   │   ├── constants/
│   │   │   ├── theme/
│   │   │   └── router/
│   │   ├── features/                 # Feature modules
│   │   │   ├── auth/
│   │   │   └── dashboard/
│   │   ├── shared/                   # Shared components
│   │   └── main.dart
│   └── pubspec.yaml
├── docker-compose.yml               # Development environment
├── Dockerfile.flutter              # Flutter container
└── README.md
```

## 🚀 Quick Start

### Option 1: Using Docker (Recommended)

1. **Start the development environment:**
```bash
docker-compose up flutter-dev
```

2. **Access the application:**
- Flutter Web: http://localhost:3000

### Option 2: Local Development

1. **Install Flutter SDK:**
```bash
# Download Flutter 3.24.5
wget https://storage.googleapis.com/flutter_infra_release/releases/stable/linux/flutter_linux_3.24.5-stable.tar.xz
tar xf flutter_linux_3.24.5-stable.tar.xz
export PATH="$PATH:`pwd`/flutter/bin"
```

2. **Setup the project:**
```bash
cd hvac_flutter_md3
flutter pub get
flutter run -d web-server --web-port 3000
```

## 📚 Documentation

### 1. Material Design 3 Integration
- **File**: `01_Material_Design_3_Expressive_Integration.md`
- **Content**: Complete guide to Material Design 3 implementation
- **Features**: Dynamic colors, adaptive layouts, motion design

### 2. Architecture Integration
- **File**: `02_Unified_Architecture_Integration_Guide.md`
- **Content**: Integration strategy for HVAC-Remix + GoBackend-Kratos + Flutter
- **Features**: gRPC communication, real-time sync, offline capabilities

### 3. Development Environment
- **File**: `03_Flutter_Development_Environment_Setup.md`
- **Content**: Complete setup guide for Flutter development
- **Features**: Project structure, dependencies, tools configuration

### 4. Package Guide
- **File**: `04_Best_Flutter_Packages_Guide.md`
- **Content**: Curated list of best Flutter packages for HVAC CRM
- **Features**: State management, UI components, charts, calendar

## 🎯 Key Features

### ✅ Implemented
- [x] Material Design 3 theme system
- [x] Riverpod state management setup
- [x] Go Router navigation
- [x] Basic dashboard UI
- [x] Docker development environment
- [x] Project structure with clean architecture

### 🚧 In Progress
- [ ] gRPC client integration
- [ ] Authentication system
- [ ] Customer management
- [ ] Job scheduling
- [ ] Equipment tracking
- [ ] Charts and analytics

### 📋 Planned
- [ ] Offline synchronization
- [ ] Voice commands
- [ ] Camera integration
- [ ] Push notifications
- [ ] Advanced reporting
- [ ] Multi-language support

## 🛠️ Technology Stack

### Frontend (Flutter)
- **Framework**: Flutter 3.24.5
- **UI**: Material Design 3
- **State Management**: Riverpod
- **Navigation**: Go Router
- **Charts**: FL Chart
- **Calendar**: Table Calendar

### Backend Integration
- **API**: gRPC with GoBackend-Kratos
- **Web**: REST API with HVAC-Remix
- **Database**: PostgreSQL
- **Cache**: Redis
- **Real-time**: WebSocket/Server-Sent Events

### Development Tools
- **Containerization**: Docker & Docker Compose
- **Code Generation**: Build Runner
- **Testing**: Flutter Test + Integration Tests
- **CI/CD**: GitHub Actions (planned)

## 🔧 Development Commands

### Flutter Commands
```bash
# Install dependencies
flutter pub get

# Run code generation
dart run build_runner build

# Run tests
flutter test

# Build for production
flutter build web
flutter build apk
```

### Docker Commands
```bash
# Start all services
docker-compose up

# Start only Flutter
docker-compose up flutter-dev

# Start with backend services
docker-compose --profile backend up

# Rebuild containers
docker-compose build
```

## 📱 Supported Platforms

- ✅ **Web** (Primary target)
- ✅ **Android** (Mobile & Tablet)
- ✅ **iOS** (Mobile & Tablet)
- ✅ **Windows** (Desktop)
- ✅ **macOS** (Desktop)
- ✅ **Linux** (Desktop)

## 🎨 Design System

### Colors
- **Primary**: HVAC Blue (#1976D2)
- **Secondary**: HVAC Orange (#FF9800)
- **Dynamic**: System-generated from wallpaper (Android 12+)

### Typography
- **Font Family**: Roboto
- **Weights**: Regular (400), Medium (500), Bold (700)
- **Scale**: Material Design 3 type scale

### Components
- **Cards**: Rounded corners (16px), elevation 2
- **Buttons**: Rounded corners (12px), Material 3 styling
- **Navigation**: Bottom navigation with Material 3 design

## 🔐 Security Features

- **Secure Storage**: Flutter Secure Storage for tokens
- **Authentication**: Integration with Kratos identity management
- **Permissions**: Granular permission handling
- **Data Encryption**: Local data encryption with Hive

## 📊 Performance Optimizations

- **Lazy Loading**: Progressive data loading
- **Image Caching**: Cached network images
- **State Management**: Efficient Riverpod providers
- **Bundle Size**: Tree shaking and code splitting

## 🧪 Testing Strategy

### Unit Tests
- Business logic testing
- Provider testing
- Utility function testing

### Widget Tests
- UI component testing
- User interaction testing
- Golden tests for visual regression

### Integration Tests
- End-to-end user flows
- API integration testing
- Performance testing

## 🚀 Deployment

### Web Deployment
```bash
flutter build web --release
# Deploy to Firebase Hosting, Vercel, or Netlify
```

### Mobile Deployment
```bash
# Android
flutter build apk --release
flutter build appbundle --release

# iOS
flutter build ios --release
```

### Desktop Deployment
```bash
# Windows
flutter build windows --release

# macOS
flutter build macos --release

# Linux
flutter build linux --release
```

## 🤝 Contributing

1. **Fork the repository**
2. **Create a feature branch**: `git checkout -b feature/amazing-feature`
3. **Commit changes**: `git commit -m 'Add amazing feature'`
4. **Push to branch**: `git push origin feature/amazing-feature`
5. **Open a Pull Request**

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🆘 Support

For support and questions:
- **Documentation**: Check the markdown files in this repository
- **Issues**: Create an issue on GitHub
- **Discussions**: Use GitHub Discussions for questions

## 🎉 Acknowledgments

- **Flutter Team**: For the amazing framework
- **Material Design Team**: For the design system
- **Riverpod**: For excellent state management
- **Community**: For the awesome packages and tools

---

**Built with ❤️ for the HVAC industry** 🏠🔧