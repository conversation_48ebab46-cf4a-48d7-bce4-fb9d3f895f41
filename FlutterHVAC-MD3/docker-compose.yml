version: '3.8'

services:
  # Flutter Development Environment
  flutter-dev:
    build:
      context: .
      dockerfile: Dockerfile.flutter
    ports:
      - "3000:3000"  # Flutter web
    volumes:
      - .:/app
      - flutter_pub_cache:/root/.pub-cache
    environment:
      - FLUTTER_WEB_PORT=3000
      - FLUTTER_WEB_HOSTNAME=0.0.0.0
    command: flutter run -d web-server --web-port 3000 --web-hostname 0.0.0.0
    networks:
      - hvac-network

  # GoBackend-Kratos (if available)
  gobackend-kratos:
    build:
      context: ../GoBackend-Kratos
      dockerfile: Dockerfile
    ports:
      - "9000:9000"  # gRPC
      - "8080:8080"  # HTTP
    environment:
      - DATABASE_URL=**********************************************/hvac_db
      - REDIS_URL=redis://redis:6379
    depends_on:
      - postgres
      - redis
    networks:
      - hvac-network
    profiles:
      - backend

  # HVAC-Remix (if available)
  hvac-remix:
    build:
      context: ../hvac-remix
      dockerfile: Dockerfile
    ports:
      - "3001:3000"  # Remix app
    environment:
      - DATABASE_URL=**********************************************/hvac_db
      - GRPC_ENDPOINT=gobackend-kratos:9000
      - NODE_ENV=development
    depends_on:
      - postgres
      - gobackend-kratos
    networks:
      - hvac-network
    profiles:
      - web

  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    ports:
      - "5432:5432"
    environment:
      POSTGRES_DB: hvac_db
      POSTGRES_USER: hvac_user
      POSTGRES_PASSWORD: hvac_pass
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./sql/init.sql:/docker-entrypoint-initdb.d/init.sql
    networks:
      - hvac-network
    profiles:
      - backend

  # Redis Cache
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - hvac-network
    profiles:
      - backend

  # Nginx Reverse Proxy
  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./nginx/ssl:/etc/nginx/ssl
    depends_on:
      - flutter-dev
    networks:
      - hvac-network
    profiles:
      - production

volumes:
  flutter_pub_cache:
  postgres_data:
  redis_data:

networks:
  hvac-network:
    driver: bridge