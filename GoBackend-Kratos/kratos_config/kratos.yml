version: v0.11.0
dsn: ************************************************************************
log:
  level: debug
  leak_sensitive_values: true
serve:
  public:
    base_url: http://localhost:4433/
    cors:
      enabled: true
      allowed_origins:
        - http://localhost:4433
        - http://localhost:8080
        - http://localhost:3000 # Assuming Flutter web might run on 3000
        - http://localhost:8080 # GoBackend
        - http://localhost:8081 # MCP
        - http://localhost:8083 # Octopus
        - http://localhost:8080 # Flutter app
      allowed_methods:
        - GET
        - POST
        - PUT
        - DELETE
        - PATCH
      allowed_headers:
        - Authorization
        - Content-Type
        - Accept
        - Origin
        - User-Agent
        - X-CSRF-Token
      exposed_headers:
        - Content-Type
        - Set-Cookie
      allow_credentials: true
  admin:
    base_url: http://localhost:4434/
selfservice:
  default_browser_return_url: http://localhost:3000/
  flows:
    login:
      ui_url: http://localhost:4433/self-service/login/browser
      after:
        password:
          hooks:
            - hook: session
    registration:
      ui_url: http://localhost:4433/self-service/registration/browser
      after:
        password:
          hooks:
            - hook: session
    settings:
      ui_url: http://localhost:4433/self-service/settings/browser
    logout:
      browser_return_url: http://localhost:3000/login